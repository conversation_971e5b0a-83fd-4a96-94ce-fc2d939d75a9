/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

import { withErrorBoundary } from "@/src/hocs/withErrorBoundary";
import { withOfflineIndicator } from "@/src/hocs/withOfflineIndicator";
import { LearnCollabStackParamsListI, StackScreenI } from "../../types";
import CreateCommunityScreen from "@/src/screens/CreateCommunity";
import CommunityRestrictionScreen from "@/src/screens/ComminityRestrictions";
import PeopleScreen from "@/src/screens/People";
import CommunityQuestionScreen from "@/src/screens/CommunityQuestion";
import EntitySearchScreen from "@/src/screens/EntitySearch";
import CreateQuestion from "@/src/screens/CreateQuestion";

const CreateCommunityScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(CreateCommunityScreen, {
    title: 'Error',
    subtitle: 'Something went wrong loading create community screen. Please try again later.',
  }),
);

const CommunityRestrictionScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(CommunityRestrictionScreen, {
    title: 'Error',
    subtitle: 'Something went wrong loading community restriction screen. Please try again later.',
  }),
);

const PeopleScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(PeopleScreen, {
    title: 'Error',
    subtitle: 'Something went wrong loading people screen. Please try again later.',
  }),
);

const CommunityQuestionScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(CommunityQuestionScreen, {
    title: 'Error',
    subtitle: 'Something went wrong loading community question screen. Please try again later.',
  }),
);

const EntitySearchScreenWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(EntitySearchScreen, {
    title: 'Search Error',
    subtitle: 'Something went wrong during search. Please try again.',
  }),
);

const CreateQuestionWithErrorBoundary = withOfflineIndicator(
  withErrorBoundary(
    CreateQuestion, {
      title: "Create Question Error",
      subtitle: "Something went wrong during posting question. Please try again."
    }
  )
)



export const screens: StackScreenI<LearnCollabStackParamsListI>[] = [
  { name: 'CreateCommunity', component: CreateCommunityScreenWithErrorBoundary },
  { name: 'CommunityRestrictions', component: CommunityRestrictionScreenWithErrorBoundary },
  { name: 'People', component: PeopleScreenWithErrorBoundary },
  { name: 'CommunityQuestion' , component: CommunityQuestionScreenWithErrorBoundary },
  { name: "SearchScreen", component: EntitySearchScreenWithErrorBoundary },
  { name: "CreateQuestion", component: CreateQuestionWithErrorBoundary  }
];
