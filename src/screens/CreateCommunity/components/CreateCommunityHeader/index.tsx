import Close from "@/src/assets/svgs/Close"
import Button from "@/src/components/Button";
import { Text, View } from "react-native"

const TOTAL_PAGES = 3

const CreateCommunityHeader = ({ currentPage, buttonTitle, onNext }: { currentPage: number; onNext: () => void; buttonTitle?: string }) => {
    return (
        <View className="flex-row justify-between">
            <View className="flex-row items-center gap-3">
                <Close width={2.25} height={2.25} />
                <Text className="text-xl font-medium">{currentPage}/{TOTAL_PAGES}</Text>
            </View>
            <View>
            <Button className="rounded-full bg-primaryGreen w-auto px-6" onPress={onNext} label={buttonTitle ?? "Next"}></Button>
            </View>
        </View>
    )
}

export default CreateCommunityHeader