import { ReactNode } from 'react';

export type MessageHandler = Record<string, (data: any) => void>;
export type SocketMessage = { type: string; data: any };
export interface SocketContextValue {
  isConnected: boolean;
  isConnecting: boolean;
  sendMessage: (type: string, data: any) => Promise<void>;
  onMessage: (type: string, handler: (data: any) => void) => void;
  removeMessageHandler: (type: string) => void;
  disconnect: (permanent?: boolean) => void;
}
export interface SocketContextProviderProps {
  children: ReactNode;
}
