import TextInput from '@/src/components/TextInput'
import { Controller } from 'react-hook-form'
import { Text } from 'react-native'
import { View } from 'react-native'
import useCreateCommunityForm from './useForm'

const CreateCommunityForm = () => {
    const { methods } = useCreateCommunityForm()
    const { control, formState: { errors } } = methods

    return (
        <View className='mt-6'>
            <Text className='text-xl font-medium'>Create Community</Text>
            <View className="flex gap-6 mt-6">
                <Controller
                    control={control}
                    name={'name'}
                    render={({ field: { onChange, value } }) => (
                        <TextInput
                            label={"Name"}
                            value={value}
                            onChangeText={onChange}
                            placeholder={`Enter forum name`}
                            error={errors?.name?.message}
                        />
                    )}
                />
                <Controller
                    control={control}
                    name={'description'}
                    render={({ field: { onChange, value } }) => (
                        <TextInput
                            label={"Description"}
                            value={value}
                            onChangeText={onChange}
                            placeholder={`Enter forum description`}
                            error={errors?.description?.message}
                        />
                    )}
                />

                
            </View>
        </View>
    )
}

export default CreateCommunityForm