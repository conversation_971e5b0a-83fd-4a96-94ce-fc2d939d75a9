/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { HomeStackParamListI, HomeScreenActionsRef } from '@/src/navigation/types';
import { screens } from './screen';

const HomeStack = createStackNavigator<HomeStackParamListI>();

type HomeRouteNames = keyof HomeStackParamListI;

type HomeStackNavigatorProps = {
  initialRouteName?: HomeRouteNames;
  homeScreenActionsRef?: HomeScreenActionsRef;
};

const HomeStackNavigator = ({
  initialRouteName = 'Home',
  homeScreenActionsRef,
}: HomeStackNavigatorProps) => (
  <HomeStack.Navigator screenOptions={{ headerShown: false }} initialRouteName={initialRouteName}>
    {screens.map(({ name, component }) =>
      name === 'Home' ? (
        <HomeStack.Screen
          key={name}
          name={name}
          children={(props) =>
            React.createElement(component as React.ComponentType<any>, {
              ...props,
              homeScreenActionsRef,
            })
          }
        />
      ) : (
        <HomeStack.Screen key={name} name={name} component={component} />
      ),
    )}
  </HomeStack.Navigator>
);

export default HomeStackNavigator;
