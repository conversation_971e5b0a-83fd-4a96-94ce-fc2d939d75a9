import RadioButton from '@/src/components/RadioButton';
import ToggleSwitch from '@/src/components/Toggle';
import React, { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

const CommunityRestrictionForm = () => {
  const [visibility, setVisibility] = useState('public');
  const [restrictPublicContributors, setRestrictPublicContributors] = useState(false);
  const [restrictPrivateContributors, setRestrictPrivateContributors] = useState(false);

  return (
    <View className="py-6 bg-white">
      <Text className="text-2xl font-semibold text-gray-900 mb-8">
        Community restrictions
      </Text>
      <View className='mb-6'>
        <RadioButton
          selected={visibility === 'public'}
          onPress={() => setVisibility('public')}
          label="Public"
          description="Anyone can view and contribute"
        >
          <ToggleSwitch
            enabled={restrictPublicContributors}
            onToggle={() => setRestrictPublicContributors(!restrictPublicContributors)}
            label="Restrict contributors"
          />
        </RadioButton>
      </View>


      <RadioButton
        selected={visibility === 'private'}
        onPress={() => setVisibility('private')}
        label="Private"
        description="Only forum members can view and contribute"
      >
        <ToggleSwitch
          enabled={restrictPrivateContributors}
          onToggle={() => setRestrictPrivateContributors(!restrictPrivateContributors)}
          label="Restrict contributors"
        />
      </RadioButton>
    </View>
  );
};

export default CommunityRestrictionForm;