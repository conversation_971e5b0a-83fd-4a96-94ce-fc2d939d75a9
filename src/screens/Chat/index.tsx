import { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  Pressable,
  Platform,
  KeyboardAvoidingView,
  ActivityIndicator,
  Image,
  FlatList,
  RefreshControl,
  PermissionsAndroid,
  Animated,
} from 'react-native';
import { type RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import { Image as ImageCompressor, Video as VideoCompressor } from 'react-native-compressor';
import ImagePicker from 'react-native-image-crop-picker';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Video from 'react-native-video';
import { pick, types } from '@react-native-documents/picker';
import { viewDocument } from '@react-native-documents/viewer';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import BottomSheet from '@/src/components/Bottomsheet/index';
import ImageViewer from '@/src/components/ImageViewer';
import CustomModal from '@/src/components/Modal';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import SafeArea from '@/src/components/SafeArea';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { formatMessageTime } from '@/src/utilities/datetime';
import type { HomeStackParamListI } from '@/src/navigation/types';
import Attachment from '@/src/assets/svgs/Attachment';
import Close from '@/src/assets/svgs/Close';
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis';
import Microphone from '@/src/assets/svgs/Microphone';
import PauseIcon from '@/src/assets/svgs/Pause';
import PlayIcon from '@/src/assets/svgs/Play';
import Send from '@/src/assets/svgs/Send';
import Trash from '@/src/assets/svgs/TrashBin';
import LinkPreview from './components/LinkPreview';
import ReplyPreview from './components/ReplyPreview';
import SwipeableMessage from './components/SwipeableMessage';
import type { MediaPreviewItem, MessageI } from './types';
import { useChatScreenHook } from './useHook';

const ChatScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<RouteProp<HomeStackParamListI, 'Chat'>>();
  const profileId = route.params.id;
  const currentUser = useSelector(selectCurrentUser);

  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [mediaOptionsVisible, setMediaOptionsVisible] = useState(false);
  const [messageOptionsVisible, setMessageOptionsVisible] = useState(false);
  const [pendingAction, setPendingAction] = useState<
    'camera' | 'gallery' | 'file' | 'video' | null
  >(null);
  const [mediaPreview, setMediaPreview] = useState<MediaPreviewItem[]>([]);
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [selectedImagePost, setSelectedImagePost] = useState<any>(null);
  const [recordingTime, setRecordingTime] = useState('00:00');
  const [isAudioPlaying, setIsAudioPlaying] = useState<{ [key: string]: boolean }>({});
  const [currentPlayingAudio, setCurrentPlayingAudio] = useState<string | null>(null);
  const [audioRecorderPlayer] = useState(() => new AudioRecorderPlayer());
  const [recordedAudioFile, setRecordedAudioFile] = useState<any>(null);
  const [isPreviewPlaying, setIsPreviewPlaying] = useState(false);
  const [previewPlaybackPosition, setPreviewPlaybackPosition] = useState(0);
  const [previewDuration, setPreviewDuration] = useState(0);
  const [isAudioUploading, setIsAudioUploading] = useState(false);
  const [audioUploadProgress, setAudioUploadProgress] = useState(0);
  const [modalClosing, setModalClosing] = useState(false);

  const textInputRef = useRef<TextInput>(null);
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const videoPlayerRef = useRef<any>(null);
  const progressBarWidth = useRef(new Animated.Value(0)).current;
  const uploadProgressAnim = useRef(new Animated.Value(0)).current;

  const {
    messages,
    profile,
    loading,
    loadingMore,
    refreshing,
    replyPreview,
    selectedMessage,
    messageText,
    sending,
    flatListRef,
    isUserOnline,
    lastSeen,
    selectedMessages,
    isSelectionMode,
    deleteOptionsVisible,
    clearChatVisible,
    isRecording,
    audioDurations,
    setMessageText,
    setDeleteOptionsVisible,
    setClearChatVisible,
    sendMessage,
    sendMediaMessage,
    uploadMedia,
    editMessage,
    handleLoadMore,
    refreshMessages,
    handleMessageLongPress,
    handleMessagePress,
    handleReply,
    handleSwipeReply,
    handleCloseReply,
    setSelectedMessage,
    startSelectionMode,
    exitSelectionMode,
    handleDeleteSelected,
    handleDeleteSelectedForMe,
    handleDeleteSelectedForEveryone,
    handleConfirmClearChat,
    canDeleteSelectedForEveryone,
    setClearChatOptionsVisible,
    clearChatOptions,
    startRecording,
    stopRecording,
    formatAudioTime,
    loadAudioDuration,
    setMessages,
    setReplyPreview,
  } = useChatScreenHook(profileId);

  useEffect(() => {
    return () => {
      if (audioRecorderPlayer) {
        audioRecorderPlayer.stopRecorder();
        audioRecorderPlayer.stopPlayer();
        audioRecorderPlayer.removeRecordBackListener();
        audioRecorderPlayer.removePlayBackListener();
      }
    };
  }, [audioRecorderPlayer]);

  const onBack = () => {
    if (isSelectionMode) {
      exitSelectionMode();
    } else {
      navigation.goBack();
    }
  };

  const handleAttachment = () => {
    setMediaOptionsVisible(true);
  };

  const handleCameraPress = () => {
    setPendingAction('camera');
    setMediaOptionsVisible(false);
  };

  const handleGalleryPress = () => {
    setPendingAction('gallery');
    setMediaOptionsVisible(false);
  };

  const handleFilePress = () => {
    setPendingAction('file');
    setMediaOptionsVisible(false);
  };

  const handleVideoPress = () => {
    setPendingAction('video');
    setMediaOptionsVisible(false);
  };

  const handleModalHide = () => {
    if (!pendingAction || modalClosing) return;

    setTimeout(() => {
      if (pendingAction === 'camera') {
        openCamera();
      } else if (pendingAction === 'gallery') {
        openGallery();
      } else if (pendingAction === 'file') {
        openFilePicker();
      } else if (pendingAction === 'video') {
        openVideoCamera();
      }
      setPendingAction(null);
    }, 300);
  };

  const openCamera = () => {
    ImagePicker.openCamera({
      width: 1024,
      height: 1024,
      cropping: false,
      mediaType: 'photo',
      includeBase64: false,
    })
      .then(async (image) => {
        try {
          const compressedImage = await ImageCompressor.compress(image.path, {
            compressionMethod: 'auto',
          });
          handleMediaSelection([{ ...image, path: compressedImage }]);
        } catch (error) {
          handleMediaSelection([image]);
        }
      })
      .catch((error) => {});
  };

  const openVideoCamera = () => {
    ImagePicker.openCamera({
      mediaType: 'video',
      includeBase64: false,
    })
      .then(async (video) => {
        try {
          const compressedVideo = await VideoCompressor.compress(
            video.path,
            { compressionMethod: 'auto' },
            (progress) => {},
          );
          handleMediaSelection([
            {
              ...video,
              path: compressedVideo,
              mime: 'video/mp4',
            },
          ]);
        } catch (error) {
          handleMediaSelection([
            {
              ...video,
              mime: 'video/mp4',
            },
          ]);
        }
      })
      .catch((error) => {});
  };

  const openGallery = () => {
    ImagePicker.openPicker({
      multiple: true,
      mediaType: 'any',
      includeBase64: false,
      maxFiles: 5,
    })
      .then(async (media) => {
        const processedMedia = await Promise.all(
          (Array.isArray(media) ? media : [media]).map(async (item) => {
            if (item.mime.startsWith('image/')) {
              try {
                const compressedPath = await ImageCompressor.compress(item.path, {
                  compressionMethod: 'auto',
                });
                return { ...item, path: compressedPath };
              } catch (error) {
                return item;
              }
            } else if (item.mime.startsWith('video/')) {
              try {
                const compressedPath = await VideoCompressor.compress(
                  item.path,
                  { compressionMethod: 'auto' },
                  (progress) => {},
                );
                return { ...item, path: compressedPath };
              } catch (error) {
                return item;
              }
            }
            return item;
          }),
        );
        handleMediaSelection(processedMedia);
      })
      .catch((error) => {});
  };

  const openFilePicker = async () => {
    try {
      const files = await pick({
        allowMultiSelection: true,
        type: [types.images, types.pdf, types.plainText, types.video, types.audio],
      });

      const formattedFiles = files.map((file) => ({
        path: file.uri,
        mime: file.type || 'application/octet-stream',
        filename: file.name,
        size: file.size,
      }));

      handleMediaSelection(formattedFiles);
    } catch (error: any) {}
  };

  const handleMediaSelection = async (selectedMedia: any[]) => {
    const tempMediaItems: MediaPreviewItem[] = selectedMedia.map((item, index) => ({
      url: item.path,
      mimeType: item.mime,
      name: item.filename || `media_${index}.${item.mime.split('/')[1]}`,
      isUploading: true,
    }));

    setMediaPreview(tempMediaItems);

    try {
      const uploadedMedia = await uploadMedia(selectedMedia);
      setMediaPreview(uploadedMedia);
    } catch (error) {
      setMediaPreview([]);
    }
  };

  const handleSendMessage = () => {
    if (editingMessageId) {
      handleSaveEdit();
      return;
    }

    if (mediaPreview.length > 0) {
      if (mediaPreview.some((item) => item.isUploading)) return;
      sendMediaMessage(mediaPreview, messageText.trim());
      setMessageText('');
      setMediaPreview([]);
    } else if (recordedAudioFile) {
      handleSendAudioMessage();
    } else if (messageText.trim()) {
      sendMessage();
      setMessageText('');
    }
  };

  const handleSendAudioMessage = async () => {
    if (isAudioUploading) return;

    try {
      setIsAudioUploading(true);
      setAudioUploadProgress(0);

      Animated.timing(uploadProgressAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: false,
      }).start();

      const simulateProgress = () => {
        const interval = setInterval(() => {
          setAudioUploadProgress((prev) => {
            if (prev >= 90) {
              clearInterval(interval);
              return prev;
            }
            return prev + Math.random() * 15;
          });
        }, 200);
        return interval;
      };

      const progressInterval = simulateProgress();

      const mediaItems = await uploadMedia([recordedAudioFile]);

      clearInterval(progressInterval);
      setAudioUploadProgress(100);

      const currentMessageText = messageText.trim();
      await sendMediaMessage(mediaItems, currentMessageText);

      setMessageText('');
      setRecordedAudioFile(null);
      setIsPreviewPlaying(false);
      setPreviewPlaybackPosition(0);
      setPreviewDuration(0);
      setIsAudioUploading(false);
      setAudioUploadProgress(0);
      uploadProgressAnim.setValue(0);
    } catch (error) {
      setIsAudioUploading(false);
      setAudioUploadProgress(0);
      uploadProgressAnim.setValue(0);
    }
  };

  const handleCancelMediaPreview = () => {
    setMediaPreview([]);
  };

  const handleCancelAudioPreview = () => {
    if (isPreviewPlaying) {
      audioRecorderPlayer.stopPlayer();
      audioRecorderPlayer.removePlayBackListener();
      setIsPreviewPlaying(false);
    }
    if (isAudioUploading) {
      setIsAudioUploading(false);
      setAudioUploadProgress(0);
      uploadProgressAnim.setValue(0);
    }
    setRecordedAudioFile(null);
    setPreviewPlaybackPosition(0);
    setPreviewDuration(0);
  };

  const handleImagePress = (message: MessageI) => {
    if (message.content.media && message.content.media.length > 0) {
      const imagePost = {
        createdAt: message.createdAt,
        Profile: {
          avatar:
            message.senderId === currentUser.profileId ? currentUser.avatar : profile?.avatar || '',
          name:
            message.senderId === currentUser.profileId ? currentUser.fullName : profile?.name || '',
          designation:
            message.senderId === currentUser.profileId
              ? currentUser.designation
              : profile?.designation,
          entity:
            message.senderId === currentUser.profileId ? currentUser.organisation : profile?.entity,
        },
        Media: message.content.media.map((media) => ({
          fileUrl: media.url,
          caption: '',
        })),
      };
      setSelectedImagePost(imagePost);
      setImageViewerVisible(true);
    }
  };

  const handleDocumentPress = async (media: any) => {
    try {
      await viewDocument({
        uri: media.url,
        mimeType: media.mimeType,
      });
    } catch (error) {}
  };

  const handleMicrophonePress = async () => {
    if (isRecording) {
      try {
        const result = await audioRecorderPlayer.stopRecorder();
        audioRecorderPlayer.removeRecordBackListener();
        setRecordingTime('00:00');

        const audioFile = {
          path: result,
          mime: 'audio/mp3',
          filename: `audio_${Date.now()}.mp3`,
        };

        setRecordedAudioFile(audioFile);
        stopRecording(null);
      } catch (error) {
        stopRecording(null);
      }
    } else {
      try {
        if (Platform.OS === 'android') {
          const grants = await PermissionsAndroid.requestMultiple([
            PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
            PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          ]);

          if (
            grants[PermissionsAndroid.PERMISSIONS.RECORD_AUDIO] !==
              PermissionsAndroid.RESULTS.GRANTED ||
            grants[PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE] !==
              PermissionsAndroid.RESULTS.GRANTED
          ) {
            return;
          }
        }

        const path = Platform.OS === 'ios' ? 'audio.m4a' : `audio_${Date.now()}.mp3`;

        await audioRecorderPlayer.startRecorder(path);
        audioRecorderPlayer.addRecordBackListener((e) => {
          const seconds = Math.floor(e.currentPosition / 1000);
          const minutes = Math.floor(seconds / 60);
          const remainingSeconds = seconds % 60;
          setRecordingTime(
            `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`,
          );
        });

        startRecording();
      } catch (error) {}
    }
  };

  const handlePlayPreviewAudio = async () => {
    if (!recordedAudioFile || isAudioUploading) return;

    try {
      if (isPreviewPlaying) {
        await audioRecorderPlayer.stopPlayer();
        audioRecorderPlayer.removePlayBackListener();
        setIsPreviewPlaying(false);
      } else {
        await audioRecorderPlayer.startPlayer(recordedAudioFile.path);
        setIsPreviewPlaying(true);

        audioRecorderPlayer.addPlayBackListener((e) => {
          setPreviewPlaybackPosition(e.currentPosition);
          setPreviewDuration(e.duration);
          progressBarWidth.setValue(e.currentPosition / e.duration);

          if (e.currentPosition === e.duration) {
            setIsPreviewPlaying(false);
            setPreviewPlaybackPosition(0);
            progressBarWidth.setValue(0);
            audioRecorderPlayer.removePlayBackListener();
          }
        });
      }
    } catch (error) {
      setIsPreviewPlaying(false);
    }
  };

  const handlePlayAudio = async (mediaUrl: string, messageId: string) => {
    try {
      if (isAudioPlaying[messageId]) {
        await audioRecorderPlayer.stopPlayer();
        audioRecorderPlayer.removePlayBackListener();
        setIsAudioPlaying((prev) => ({ ...prev, [messageId]: false }));
        setCurrentPlayingAudio(null);
      } else {
        if (currentPlayingAudio) {
          await audioRecorderPlayer.stopPlayer();
          setIsAudioPlaying((prev) => ({ ...prev, [currentPlayingAudio]: false }));
        }

        await audioRecorderPlayer.startPlayer(mediaUrl);
        setIsAudioPlaying((prev) => ({ ...prev, [messageId]: true }));
        setCurrentPlayingAudio(messageId);

        audioRecorderPlayer.addPlayBackListener((e) => {
          if (e.currentPosition === e.duration) {
            setIsAudioPlaying((prev) => ({ ...prev, [messageId]: false }));
            setCurrentPlayingAudio(null);
            audioRecorderPlayer.removePlayBackListener();
          }
        });
      }
    } catch (error) {
      setIsAudioPlaying((prev) => ({ ...prev, [messageId]: false }));
    }
  };

  const getDateLabel = (timestamp: Date): string => {
    const messageDate = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const messageDateOnly = new Date(
      messageDate.getFullYear(),
      messageDate.getMonth(),
      messageDate.getDate(),
    );
    const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const yesterdayOnly = new Date(
      yesterday.getFullYear(),
      yesterday.getMonth(),
      yesterday.getDate(),
    );

    if (messageDateOnly.getTime() === todayOnly.getTime()) {
      return 'Today';
    } else if (messageDateOnly.getTime() === yesterdayOnly.getTime()) {
      return 'Yesterday';
    } else {
      return messageDate.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'short',
        day: 'numeric',
      });
    }
  };

  const canEditMessage = (message: MessageI): boolean => {
    if (message.senderId !== currentUser.profileId || message.deletedForAll) return false;
    const editTimeLimit = new Date(message.createdAt);
    editTimeLimit.setMinutes(editTimeLimit.getMinutes() + 15);
    return new Date() <= editTimeLimit;
  };

  const handleMessageOptions = (message: MessageI, fromMessageBody = false) => {
    if (fromMessageBody) {
      setSelectedMessage(message);
      setMessageOptionsVisible(true);
    }
  };

  const handleMessageLongPressStart = (message: MessageI) => {
    longPressTimerRef.current = setTimeout(() => {
      if (!isSelectionMode) {
        startSelectionMode(message.id);
      }
    }, 500);
  };

  const handleMessageLongPressEnd = () => {
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }
  };

  const handleMessageTap = (message: MessageI, fromMessageBody = false) => {
    if (isSelectionMode) {
      handleMessagePress(message);
    } else if (fromMessageBody) {
      handleMessageOptions(message, true);
    }
  };

  const handleEdit = () => {
    if (selectedMessage) {
      setReplyPreview({
        ...selectedMessage,
        isEditing: true,
      });

      setEditingMessageId(selectedMessage.id);
      setMessageText(selectedMessage.content.text || '');
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 100);
    }
    setMessageOptionsVisible(false);
    setSelectedMessage(null);
  };

  const handleReplyToMessage = () => {
    if (selectedMessage && !selectedMessage.deletedForAll) {
      handleReply();
    }
    setMessageOptionsVisible(false);
    setSelectedMessage(null);
  };

  const handleSaveEdit = () => {
    if (editingMessageId && messageText.trim()) {
      editMessage(editingMessageId, messageText.trim());

      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === editingMessageId
            ? {
                ...msg,
                content: { ...msg.content, text: messageText.trim() },
                editedAt: new Date(),
              }
            : msg,
        ),
      );

      setEditingMessageId(null);
      setMessageText('');
      setReplyPreview(null);
    }
  };

  const handleTextInputChange = (text: string) => {
    setMessageText(text);
  };

  const extractUrls = (text: string): string[] => {
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    return text.match(urlRegex) || [];
  };

  const renderMediaDisplay = (mediaItems: any[], isMyMessage: boolean, message: MessageI) => {
    if (!mediaItems || mediaItems.length === 0) return null;

    const firstMedia = mediaItems[0];
    const mediaCount = mediaItems.length;

    const isImage = firstMedia.mimeType.startsWith('image/') || firstMedia.mimeType === 'JPEG';
    const isPdf = firstMedia.mimeType === 'application/pdf' || firstMedia.mimeType === 'PDF';
    const isText = firstMedia.mimeType === 'text/plain' || firstMedia.mimeType === 'TXT';
    const isAudio =
      firstMedia.mimeType === 'audio/mpeg' ||
      firstMedia.mimeType.includes('mp3') ||
      firstMedia.mimeType === 'MP3';
    const isVideo =
      firstMedia.mimeType === 'video/mp4' ||
      firstMedia.mimeType.includes('mp4') ||
      firstMedia.mimeType === 'MP4';

    return (
      <Pressable
        onPress={() => {
          if (isSelectionMode) {
            handleMessagePress(message);
          } else if (isImage) {
            handleImagePress(message);
          } else if (isPdf || isText) {
            handleDocumentPress(firstMedia);
          }
        }}
        className="rounded-2xl overflow-hidden mb-2 relative"
        style={{ minWidth: 200, maxWidth: 280 }}
      >
        {isImage ? (
          <Image
            source={{ uri: firstMedia.url }}
            style={{ width: '100%', height: 200, minWidth: 200 }}
            resizeMode="cover"
          />
        ) : isVideo ? (
          <View style={{ width: '100%', height: 200, minWidth: 200 }}>
            <Video
              ref={videoPlayerRef}
              source={{ uri: firstMedia.url }}
              style={{ width: '100%', height: '100%' }}
              resizeMode="cover"
              controls={true}
              paused={true}
              poster={firstMedia.thumbnail || undefined}
              posterResizeMode="cover"
            />
            <View className="absolute bottom-2 left-2 bg-black/70 px-2 py-1 rounded-md">
              <Text className="text-white text-xs font-medium">Video</Text>
            </View>
            <View className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <View className="w-12 h-12 rounded-full bg-black/50 items-center justify-center">
                <PlayIcon width={20} height={20} color="#ffffff" />
              </View>
            </View>
          </View>
        ) : isAudio ? (
          <View className="w-full p-3" style={{ minWidth: 200, maxWidth: 280 }}>
            <Pressable
              className="flex-row items-center gap-3"
              onPress={() => {
                if (!audioDurations[message.id]) {
                  loadAudioDuration(message.id, firstMedia.url);
                }
                handlePlayAudio(firstMedia.url, message.id);
              }}
            >
              <View className="w-8 h-8 rounded-full bg-gray-300 items-center justify-center">
                {isAudioPlaying[message.id] ? (
                  <PauseIcon width={2} height={2} color="#666666" />
                ) : (
                  <PlayIcon width={2} height={2} color="#666666" />
                )}
              </View>
              <View className="flex-1">
                <Text className="text-xs text-gray-500">
                  {audioDurations[message.id]
                    ? formatAudioTime(audioDurations[message.id])
                    : 'Audio'}
                </Text>
              </View>
            </Pressable>
          </View>
        ) : (
          <View className="w-full p-4 bg-gray-200 rounded-2xl" style={{ minHeight: 120 }}>
            <View className="items-center justify-center flex-1">
              <Text className="text-4xl mb-2">{isPdf ? '📄' : isText ? '📝' : '📎'}</Text>
              <Text
                className={`${isMyMessage ? 'text-gray-700' : 'text-gray-600'} text-center font-medium`}
              >
                {isPdf ? 'PDF Document' : isText ? 'Text File' : 'File'}
              </Text>
              <Text
                className={`${isMyMessage ? 'text-gray-600' : 'text-gray-500'} text-xs text-center mt-1`}
              >
                Tap to view
              </Text>
            </View>
          </View>
        )}
        {mediaCount > 1 && (
          <View className="absolute top-2 right-2 bg-black/70 rounded-full px-2 py-1">
            <Text className="text-white text-xs font-medium">1/{mediaCount}</Text>
          </View>
        )}
        {isSelectionMode && selectedMessages.has(message.id) && (
          <View className="absolute top-2 left-2 bg-green-800 rounded-full w-6 h-6 items-center justify-center">
            <Text className="text-white text-xs font-bold">✓</Text>
          </View>
        )}
      </Pressable>
    );
  };

  const renderMediaPreview = () => {
    if (mediaPreview.length === 0) return null;

    const firstMedia = mediaPreview[0];
    const mediaCount = mediaPreview.length;
    const isImage = firstMedia.mimeType.startsWith('image/') || firstMedia.mimeType === 'JPEG';
    const isPdf = firstMedia.mimeType === 'application/pdf' || firstMedia.mimeType === 'PDF';
    const isText = firstMedia.mimeType === 'text/plain' || firstMedia.mimeType === 'TXT';
    const isAudio =
      firstMedia.mimeType === 'audio/mpeg' ||
      firstMedia.mimeType.includes('mp3') ||
      firstMedia.mimeType === 'MP3';
    const isVideo =
      firstMedia.mimeType === 'video/mp4' ||
      firstMedia.mimeType.includes('mp4') ||
      firstMedia.mimeType === 'MP4';

    return (
      <View className="rounded-2xl overflow-hidden mb-2 relative">
        {isImage ? (
          <Image source={{ uri: firstMedia.url }} className="w-full h-48" resizeMode="cover" />
        ) : isVideo ? (
          <View className="w-full h-48 bg-black relative">
            <Video
              source={{ uri: firstMedia.url }}
              style={{ width: '100%', height: '100%' }}
              resizeMode="contain"
              controls={true}
              paused={true}
            />
            <View className="absolute bottom-2 left-2 bg-black/70 px-2 py-1 rounded-md">
              <Text className="text-white text-xs font-medium">Video</Text>
            </View>
            <View className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <View className="w-12 h-12 rounded-full bg-black/50 items-center justify-center">
                <PlayIcon width={2} height={2} color="#ffffff" />
              </View>
            </View>
            {firstMedia.isUploading && (
              <View className="absolute inset-0 bg-black/50 justify-center items-center">
                <ActivityIndicator size="large" color="#ffffff" />
                <Text className="text-white mt-2 text-sm">Uploading video...</Text>
              </View>
            )}
          </View>
        ) : isAudio ? (
          <View className="w-full p-4 bg-gray-100 rounded-2xl">
            <View className="flex-row items-center gap-3">
              <View className="w-10 h-10 rounded-full bg-gray-300 items-center justify-center">
                <PlayIcon width={1.5} height={1.5} color="#666666" />
              </View>
              <View className="flex-1">
                <Text className="text-gray-700 font-medium">Audio</Text>
                <Text className="text-gray-500 text-xs mt-1">Ready to send</Text>
                {messageText.trim() && (
                  <Text className="text-xs text-gray-700 mt-1" numberOfLines={1}>
                    Caption: {messageText}
                  </Text>
                )}
              </View>
            </View>
            {firstMedia.isUploading && (
              <View className="absolute inset-0 bg-gray-100/90 justify-center items-center rounded-2xl">
                <ActivityIndicator size="small" color="#666666" />
                <Text className="text-gray-600 mt-2 text-sm">Uploading audio...</Text>
              </View>
            )}
          </View>
        ) : (
          <View className="w-full p-3 bg-gray-200 rounded-2xl h-48 justify-center items-center">
            <Text className="text-4xl mb-2">{isPdf ? '📄' : isText ? '📝' : '📎'}</Text>
            <Text className="text-gray-600 text-center font-medium">
              {isPdf ? 'PDF Document' : isText ? 'Text File' : 'File'}
            </Text>
            {messageText.trim() && (
              <Text className="text-xs text-gray-700 mt-3" numberOfLines={2}>
                Caption: {messageText}
              </Text>
            )}
          </View>
        )}
        {firstMedia.isUploading && (isImage || (!isVideo && !isAudio)) && (
          <View className="absolute inset-0 bg-black/50 justify-center items-center">
            <ActivityIndicator size="large" color="#ffffff" />
            <Text className="text-white mt-2">Uploading...</Text>
          </View>
        )}
        {mediaCount > 1 && (
          <View className="absolute top-2 left-2 bg-black/70 rounded-full px-2 py-1">
            <Text className="text-white text-xs font-medium">1/{mediaCount}</Text>
          </View>
        )}
        <Pressable
          className="absolute top-2 right-2 bg-black/50 rounded-full p-1"
          onPress={handleCancelMediaPreview}
        >
          <Close color="#ffffff" width={2} height={2} />
        </Pressable>
      </View>
    );
  };

  const renderAudioPreview = () => {
    if (!recordedAudioFile) return null;

    return (
      <View className="px-4 mb-3">
        <View className="bg-gray-100 rounded-xl p-4 relative">
          {isAudioUploading && (
            <View className="absolute inset-0 flex items-center justify-center bg-gray-100/95 rounded-xl z-10">
              <View className="w-full px-6">
                <ActivityIndicator size="small" color="#666666" />
                <Text className="text-gray-600 text-sm font-medium text-center mt-2">
                  Uploading audio... {Math.round(audioUploadProgress)}%
                </Text>
                <View className="w-full h-1.5 bg-gray-200 rounded-full mt-2 overflow-hidden">
                  <Animated.View
                    className="h-full bg-green-600 rounded-full"
                    style={{
                      width: uploadProgressAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: ['0%', '100%'],
                      }),
                    }}
                  />
                </View>
              </View>
            </View>
          )}

          <View className="flex-row items-center gap-3">
            <Pressable
              onPress={handlePlayPreviewAudio}
              disabled={isAudioUploading}
              className={`w-10 h-10 rounded-full items-center justify-center ${
                isAudioUploading ? 'bg-gray-200' : 'bg-gray-300'
              }`}
            >
              {isPreviewPlaying ? (
                <PauseIcon
                  width={1.5}
                  height={1.5}
                  color={isAudioUploading ? '#999999' : '#666666'}
                />
              ) : (
                <PlayIcon
                  width={1.5}
                  height={1.5}
                  color={isAudioUploading ? '#999999' : '#666666'}
                />
              )}
            </Pressable>
            <View className="flex-1">
              <Text className={`text-xs ${isAudioUploading ? 'text-gray-400' : 'text-gray-500'}`}>
                {isAudioUploading
                  ? 'Preparing...'
                  : `${formatAudioTime(previewPlaybackPosition)} / ${formatAudioTime(previewDuration || 0)}`}
              </Text>
              {messageText.trim() && (
                <Text className="text-xs text-gray-700 mt-1" numberOfLines={1}>
                  Caption: {messageText}
                </Text>
              )}
            </View>
            <Pressable
              onPress={handleCancelAudioPreview}
              disabled={isAudioUploading}
              className={`w-8 h-8 rounded-full items-center justify-center ${
                isAudioUploading ? 'bg-gray-100' : 'bg-gray-200'
              }`}
            >
              <Close color={isAudioUploading ? '#999999' : '#666666'} width={2} height={2} />
            </Pressable>
          </View>
        </View>
      </View>
    );
  };

  const renderMessage = ({ item: msg, index }: { item: MessageI; index: number }) => {
    const time = formatMessageTime(new Date(msg.createdAt));
    const isMyMessage = msg.senderId === currentUser.profileId;
    const replyToMessage = msg.replyTo ? messages.find((m) => m.id === msg.replyTo) : null;
    const hasMedia = msg.content.media && msg.content.media.length > 0;
    const hasText = msg.content.text && msg.content.text.trim();
    const isSelected = selectedMessages.has(msg.id);
    const urls = hasText ? extractUrls(msg.content.text!) : [];
    const hasLinks = urls.length > 0;

    const prevMessage = index > 0 ? messages[index - 1] : null;
    const showDateLabel =
      !prevMessage || getDateLabel(msg.createdAt) !== getDateLabel(prevMessage.createdAt);

    const renderText = (text: string) => {
      if (!text) return null;

      const urlRegex = /(https?:\/\/[^\s]+)/g;
      const parts = text.split(urlRegex);
      const matches = text.match(urlRegex) || [];

      return (
        <Text className="text-sm text-gray-900">
          {parts.map((part, i) => {
            const isUrl = matches.includes(part as never);
            return isUrl ? (
              <Text key={i} className="text-green-800">
                {part}
              </Text>
            ) : (
              <Text key={i}>{part}</Text>
            );
          })}
          {msg.editedAt && <Text className="text-xs text-gray-400 ml-1"> (edited)</Text>}
        </Text>
      );
    };

    return (
      <View>
        {showDateLabel && (
          <Text className="text-center text-gray-500 font-semibold my-3">
            {getDateLabel(msg.createdAt)}
          </Text>
        )}
        <View className="mb-2 px-4">
          <SwipeableMessage
            message={msg}
            onReply={handleSwipeReply}
            onLongPress={handleMessageLongPress}
          >
            <Pressable
              onPress={() => handleMessageTap(msg, true)}
              onPressIn={() => handleMessageLongPressStart(msg)}
              onPressOut={handleMessageLongPressEnd}
              onLongPress={() => {
                if (!isSelectionMode) {
                  startSelectionMode(msg.id);
                }
              }}
              className={`max-w-[80%] p-3 rounded-xl ${
                isMyMessage
                  ? 'bg-violet-100 border border-violet-100 self-end'
                  : 'bg-white border border-gray-100 self-start'
              } ${msg.deletedForAll ? 'opacity-60' : ''} ${isSelected ? 'bg-green-50 border-green-200' : ''}`}
            >
              {isSelectionMode && (
                <View className="absolute -top-2 -left-2 bg-green-500 rounded-full w-6 h-6 items-center justify-center z-10">
                  <Text className="text-white text-xs font-bold">{isSelected ? '✓' : ''}</Text>
                </View>
              )}

              {replyToMessage && (
                <View className="mb-2 p-2 bg-[#F5F3FF] border-l-2 border-[#D2C9FF]">
                  <Text className="text-xs font-medium text-gray-600">
                    {replyToMessage.senderId === currentUser.profileId
                      ? 'You'
                      : profile?.name || 'User'}
                  </Text>
                  <Text className="text-xs text-gray-700" numberOfLines={1}>
                    {replyToMessage.deletedForAll
                      ? 'This message has been deleted'
                      : replyToMessage.content.text || 'Media message'}
                  </Text>
                </View>
              )}

              <>
                {hasMedia && renderMediaDisplay(msg.content.media as any, isMyMessage, msg)}

                {hasText &&
                  (msg.deletedForAll ? (
                    <Text className="text-sm text-gray-900">This message has been deleted</Text>
                  ) : (
                    renderText(msg.content.text!)
                  ))}

                {hasLinks && !msg.deletedForAll && <LinkPreview url={urls[0]} />}
              </>

              <Text className="text-xs text-gray-400 mt-1 text-right">{time}</Text>
            </Pressable>
          </SwipeableMessage>
        </View>
      </View>
    );
  };

  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View className="py-4">
        <ActivityIndicator size="small" color="#448600" />
      </View>
    );
  };

  const getBottomSheetHeight = (type: 'media' | 'delete' | 'chat' | 'confirm' | 'message') => {
    const baseHeight = RFPercentage(7);
    const itemHeight = RFPercentage(4);
    const padding = RFPercentage(4);

    switch (type) {
      case 'media':
        return baseHeight + itemHeight * 4 + padding;
      case 'delete':
        const deleteItems = canDeleteSelectedForEveryone() ? 2 : 1;
        return baseHeight + itemHeight * deleteItems + padding;
      case 'chat':
        return baseHeight + itemHeight + padding;
      case 'message':
        let messageOptions = 2;
        if (selectedMessage && canEditMessage(selectedMessage)) messageOptions++;
        if (selectedMessage && selectedMessage.senderId === currentUser.profileId) messageOptions++;
        return baseHeight + itemHeight * messageOptions;
      case 'confirm':
        return RFPercentage(30);
      default:
        return RFPercentage(25);
    }
  };

  useEffect(() => {
    return () => {
      if (audioRecorderPlayer && isAudioPlaying) {
        audioRecorderPlayer.stopPlayer();
        audioRecorderPlayer.removePlayBackListener();
      }
    };
  }, []);

  if (loading) {
    return (
      <SafeArea>
        <View className="flex-1 justify-center items-center bg-white">
          <ActivityIndicator size="small" color="#448600" />
          <Text className="text-gray-600 mt-3 text-base">Loading chat...</Text>
        </View>
      </SafeArea>
    );
  }

  return (
    <SafeArea>
      <KeyboardAvoidingView
        className="flex-1 bg-white"
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <View className="flex-row items-center gap-2 px-2 border-b border-gray-200 bg-white z-10">
          <BackButton label="" onBack={onBack} />
          {isSelectionMode ? (
            <View className="flex-1 flex-row items-center justify-between">
              <Text className="text-base font-medium">{selectedMessages.size} selected</Text>
              <Pressable
                onPress={handleDeleteSelected}
                className="p-2 rounded-full"
                disabled={selectedMessages.size === 0}
              >
                <Trash color="#ef4444" width={2} height={2} />
              </Pressable>
            </View>
          ) : (
            <View className="flex-1 flex-row items-center justify-between">
              <View className="flex-row items-center gap-2">
                <UserAvatar
                  avatarUri={profile?.avatar || ''}
                  name={profile?.name || ''}
                  width={37}
                  height={37}
                />
                <View>
                  <Text className="text-base font-medium">{profile?.name || 'User'}</Text>
                  <Text className="text-xs text-gray-500">
                    {isUserOnline ? 'Online' : lastSeen ? `Last seen ${lastSeen}` : 'Offline'}
                  </Text>
                </View>
              </View>
              <Pressable onPress={() => setClearChatOptionsVisible(true)} className="p-2">
                <HorizontalEllipsis color="#666666" width={4} height={4} />
              </Pressable>
            </View>
          )}
        </View>

        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{ paddingVertical: 12 }}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          ListFooterComponent={renderFooter}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={refreshMessages}
              tintColor="#448600"
            />
          }
          maintainVisibleContentPosition={{
            minIndexForVisible: 0,
            autoscrollToTopThreshold: 10,
          }}
        />

        {!isSelectionMode && (
          <View className="border-t border-gray-200 bg-white py-3">
            {replyPreview && <ReplyPreview message={replyPreview} onClose={handleCloseReply} />}

            {mediaPreview.length > 0 && (
              <View className="px-4 pt-3">
                {renderMediaPreview()}
                <Text className="text-sm text-gray-500 mb-2">Add a caption (optional)</Text>
              </View>
            )}

            {recordedAudioFile && renderAudioPreview()}

            {isRecording && (
              <View className="px-4 mb-3">
                <View className="flex-row items-center justify-between bg-gray-100 p-3 rounded-lg">
                  <View className="flex-row items-center gap-2">
                    <View className="h-3 w-3 rounded-full bg-gray-500 animate-pulse" />
                    <Text className="text-gray-700 font-medium">Recording {recordingTime}</Text>
                  </View>
                  <Pressable
                    className="bg-gray-500 rounded-full p-2"
                    onPress={handleMicrophonePress}
                  >
                    <Text className="text-white font-bold">STOP</Text>
                  </Pressable>
                </View>
              </View>
            )}

            <View className="flex-row items-end px-4 gap-3">
              <View className="flex-1 relative border rounded-full bg-[#F3ECEC] border-[#DEDEDE] pl-4 pr-14 py-3">
                <TextInput
                  ref={textInputRef}
                  value={messageText}
                  onChangeText={handleTextInputChange}
                  placeholder={
                    mediaPreview.length > 0 || recordedAudioFile
                      ? 'Add a caption...'
                      : replyPreview
                        ? replyPreview.isEditing
                          ? 'Edit message...'
                          : 'Reply to message...'
                        : 'Type a message'
                  }
                  placeholderTextColor="#9CA3AF"
                  className="text-sm text-gray-900"
                  textAlignVertical="center"
                  editable={!sending && !isRecording && !isAudioUploading}
                />
                <Pressable
                  onPress={handleSendMessage}
                  disabled={
                    sending ||
                    isRecording ||
                    isAudioUploading ||
                    (!messageText.trim() &&
                      !mediaPreview.length &&
                      !recordedAudioFile &&
                      !editingMessageId) ||
                    mediaPreview.some((item) => item.isUploading)
                  }
                  className={`absolute right-0 bottom-0 w-11 h-11 rounded-full items-center justify-center ${
                    (!messageText.trim() &&
                      !mediaPreview.length &&
                      !recordedAudioFile &&
                      !editingMessageId) ||
                    isAudioUploading ||
                    mediaPreview.some((item) => item.isUploading)
                      ? 'bg-gray-400/50'
                      : 'bg-green-800'
                  }`}
                >
                  {isAudioUploading ? (
                    <ActivityIndicator size="small" color="#ffffff" />
                  ) : (
                    <Send color="#ffffff" width={2} height={2} />
                  )}
                </Pressable>
              </View>
              <Pressable
                className="w-11 h-11 rounded-full items-center justify-center"
                onPress={handleAttachment}
                disabled={
                  sending ||
                  mediaPreview.length > 0 ||
                  isRecording ||
                  recordedAudioFile ||
                  isAudioUploading ||
                  editingMessageId !== null
                }
              >
                <Attachment color="#666666" width={4.5} height={4.5} />
              </Pressable>
              <Pressable
                className={`w-11 h-11 rounded-full items-center justify-center ${isRecording ? 'bg-gray-500' : ''}`}
                onPress={handleMicrophonePress}
                disabled={
                  sending ||
                  mediaPreview.length > 0 ||
                  recordedAudioFile ||
                  isAudioUploading ||
                  editingMessageId !== null
                }
              >
                <Microphone color={isRecording ? '#ffffff' : '#666666'} width={3} height={3} />
              </Pressable>
            </View>
          </View>
        )}
      </KeyboardAvoidingView>

      <BottomSheet
        onModalHide={handleModalHide}
        height={getBottomSheetHeight('media')}
        visible={mediaOptionsVisible}
        onClose={() => {
          setMediaOptionsVisible(false);
          setPendingAction(null);
        }}
      >
        <OptionsMenu>
          <OptionItem label="File" onPress={handleFilePress} />
          <View className="h-[1px] bg-gray-200" />
          <OptionItem label="Gallery" onPress={handleGalleryPress} />
          <View className="h-[1px] bg-gray-200" />
          <OptionItem label="Camera" onPress={handleCameraPress} />
          <View className="h-[1px] bg-gray-200" />
          <OptionItem label="Video" onPress={handleVideoPress} />
        </OptionsMenu>
      </BottomSheet>

      {!selectedMessage?.deletedForAll && (
        <BottomSheet
          onModalHide={() => {}}
          height={getBottomSheetHeight('message')}
          visible={messageOptionsVisible}
          onClose={() => setMessageOptionsVisible(false)}
        >
          <OptionsMenu>
            <OptionItem label="Reply" onPress={handleReplyToMessage} />
            <View className="h-[1px] bg-gray-200" />
            {selectedMessage && canEditMessage(selectedMessage) && (
              <>
                <OptionItem label="Edit" onPress={handleEdit} />
                <View className="h-[1px] bg-gray-200" />
              </>
            )}
          </OptionsMenu>
        </BottomSheet>
      )}

      <BottomSheet
        onModalHide={() => {}}
        height={getBottomSheetHeight('delete')}
        visible={deleteOptionsVisible}
        onClose={() => setDeleteOptionsVisible(false)}
      >
        <OptionsMenu>
          <OptionItem label="Delete for me" onPress={handleDeleteSelectedForMe} />
          <View className="h-[1px] bg-gray-200" />
          {canDeleteSelectedForEveryone() && (
            <>
              <OptionItem label="Delete for everyone" onPress={handleDeleteSelectedForEveryone} />
              <View className="h-[1px] bg-gray-200" />
            </>
          )}
        </OptionsMenu>
      </BottomSheet>

      <BottomSheet
        onModalHide={() => {}}
        height={getBottomSheetHeight('chat')}
        visible={clearChatOptions}
        onClose={() => setClearChatOptionsVisible(false)}
      >
        <OptionsMenu>
          <OptionItem
            label="Clear chat"
            onPress={() => {
              setClearChatOptionsVisible(false);
              setTimeout(() => {
                setClearChatVisible(true);
              }, 300);
            }}
          />
        </OptionsMenu>
      </BottomSheet>

      <CustomModal
        isVisible={clearChatVisible}
        onCancel={() => {
          setModalClosing(true);
          setClearChatVisible(false);
          setTimeout(() => {
            setModalClosing(false);
          }, 300);
        }}
        onConfirm={() => {
          handleConfirmClearChat();
          setModalClosing(true);
          setTimeout(() => {
            setModalClosing(false);
          }, 300);
        }}
        title="Clear Chat"
        description="Are you sure you want to clear this chat? This action cannot be undone."
        confirmButtonVariant="danger"
        cancelText="Cancel"
        confirmText="Clear"
      />

      {selectedImagePost && (
        <ImageViewer
          isVisible={imageViewerVisible}
          onClose={() => {
            setImageViewerVisible(false);
            setSelectedImagePost(null);
          }}
          post={selectedImagePost}
        />
      )}
    </SafeArea>
  );
};

export default ChatScreen;
