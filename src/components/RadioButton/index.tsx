import { Text, TouchableOpacity, View } from "react-native";
import { RadioButtonPropsI } from "./types";

const RadioButton = ({ selected, onPress, label, description, children }: RadioButtonPropsI) => (
    <TouchableOpacity onPress={onPress}>
        <View className="flex-row items-start">
            <View className="mt-1 mr-4">
                <View className="w-6 h-6 rounded-full border-2 border-gray-400 flex items-center justify-center">
                    {selected && (
                        <View className="w-3 h-3 rounded-full bg-primaryGreen" />
                    )}
                </View>
            </View>
            <View className="flex-1">
                <Text className="text-lg font-medium text-gray-900 mb-2">{label}</Text>
                {description ? <Text className="text-base text-gray-600 mb-4">{description}</Text> : <></>}
                {children}
            </View>
        </View>
    </TouchableOpacity>
);
export default RadioButton;