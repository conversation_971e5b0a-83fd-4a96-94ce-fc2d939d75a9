import { createContext, useContext, useEffect, useRef, useState } from 'react';
import Config from 'react-native-config';
import useStorage from '@/src/hooks/storage';
import {
  MessageHandler,
  SocketContextProviderProps,
  SocketContextValue,
  SocketMessage,
} from './types';

const SocketContext = createContext<SocketContextValue | null>(null);

const getBaseUrl = () => {
  if (Config.ENV === 'development') {
    return 'localhost:4003/ws/chat';
  }
  const cleanBaseUrl = Config.BASE_URL?.replace(/^https?:\/\//, '') || '';
  return `${cleanBaseUrl}/ws/chat`;
};

const baseUrl = getBaseUrl();

const SocketContextProvider = ({ children }: SocketContextProviderProps) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [profileId, setProfileId] = useState<string | null>(null);
  const { getStorage } = useStorage();

  const socketRef = useRef<WebSocket | null>(null);
  const messageHandlersRef = useRef<MessageHandler>({});
  const reconnectAttemptsRef = useRef(0);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const connectionPromiseRef = useRef<Promise<void> | null>(null);

  useEffect(() => {
    const loadProfileId = async () => {
      try {
        const storedProfileId = await getStorage('userProfileId');
        setProfileId(storedProfileId ?? null);
      } catch (error) {
        console.error('Error loading profileId:', error);
      }
    };
    loadProfileId();
  }, [getStorage]);

  const connect = () => {
    if (!profileId) {
      return Promise.reject(new Error('No profileId available'));
    }

    if (isConnected || isConnecting || socketRef.current?.readyState === WebSocket.OPEN) {
      return Promise.resolve();
    }

    if (socketRef.current?.readyState === WebSocket.CONNECTING) {
      return connectionPromiseRef.current || Promise.resolve();
    }

    setIsConnecting(true);
    connectionPromiseRef.current = new Promise<void>((resolve, reject) => {
      try {
        const url = `${Config.ENV === 'production' ? 'wss' : 'ws'}://${baseUrl}/${profileId}`;
        socketRef.current = new WebSocket(url);

        const connectionTimeout = setTimeout(() => {
          setIsConnecting(false);
          connectionPromiseRef.current = null;
          if (socketRef.current?.readyState === WebSocket.CONNECTING) {
            socketRef.current.close();
            reject(new Error('Connection timeout'));
          }
        }, 10000);

        socketRef.current.onopen = () => {
          clearTimeout(connectionTimeout);
          setIsConnected(true);
          setIsConnecting(false);
          reconnectAttemptsRef.current = 0;
          connectionPromiseRef.current = null;
          resolve();
        };

        socketRef.current.onclose = () => {
          clearTimeout(connectionTimeout);
          setIsConnected(false);
          setIsConnecting(false);
          connectionPromiseRef.current = null;
          attemptReconnect();
        };

        socketRef.current.onerror = (error) => {
          clearTimeout(connectionTimeout);
          setIsConnecting(false);
          connectionPromiseRef.current = null;
          if (!isConnected) reject(error);
        };

        socketRef.current.onmessage = (event) => {
          try {
            const message: SocketMessage = JSON.parse(event.data);
            const { type, data } = message;
            if (type === 'connection-established') return;
            if (type && messageHandlersRef.current[type]) {
              messageHandlersRef.current[type](data);
            }
          } catch (error) {
            console.error('Error processing message:', error);
          }
        };
      } catch (error) {
        setIsConnecting(false);
        connectionPromiseRef.current = null;
        reject(error);
      }
    });

    return connectionPromiseRef.current;
  };

  const attemptReconnect = () => {
    if (reconnectAttemptsRef.current >= 5) return;
    if (isConnected || isConnecting) return;

    if (reconnectTimeoutRef.current) clearTimeout(reconnectTimeoutRef.current);

    const delay = Math.min(30000, 3000 * Math.pow(1.5, reconnectAttemptsRef.current));
    reconnectTimeoutRef.current = setTimeout(() => {
      reconnectAttemptsRef.current++;
      connect().catch(() => {});
    }, delay);
  };

  const disconnect = (permanent = false) => {
    if (socketRef.current) {
      socketRef.current.close(1000);
      socketRef.current = null;
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    connectionPromiseRef.current = null;
    setIsConnected(false);
    setIsConnecting(false);
  };

  const sendMessage = (type: string, data: any) => {
    if (!isConnected || !socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
      return Promise.reject(new Error('Socket not connected'));
    }

    try {
      const message = JSON.stringify({ type, data });
      socketRef.current.send(message);
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  };

  const onMessage = (type: string, handler: (data: any) => void) => {
    messageHandlersRef.current[type] = handler;
  };

  const removeMessageHandler = (type: string) => {
    delete messageHandlersRef.current[type];
  };

  useEffect(() => {
    if (!profileId) {
      return;
    }

    const heartbeatInterval = setInterval(() => {
      if (isConnected && socketRef.current?.readyState === WebSocket.OPEN) {
        sendMessage('ping', {}).catch(() => {
          socketRef.current?.close();
        });
      }
    }, 30000);

    connect().catch(() => {});

    return () => {
      clearInterval(heartbeatInterval);
      disconnect();
    };
  }, [profileId]);

  const value: SocketContextValue = {
    isConnected,
    isConnecting,
    sendMessage,
    onMessage,
    removeMessageHandler,
    disconnect,
  };

  return <SocketContext.Provider value={value}>{children}</SocketContext.Provider>;
};

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketContextProvider');
  }
  return context;
};

export default SocketContextProvider;
