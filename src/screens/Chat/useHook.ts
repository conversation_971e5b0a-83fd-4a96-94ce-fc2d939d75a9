import { useState, useEffect, useRef } from 'react';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import { URL } from 'react-native-url-polyfill';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { uploadFileWithPresignedUrl } from '@/src/utilities/upload/upload';
import { useSocket } from '@/src/context/providers/SocketProvider';
import {
  findAllSpecificProfileChats,
  deleteSpecificProfileChat,
} from '@/src/networks/chat/individual';
import type { MessageData } from '@/src/networks/chat/types';
import { fetchProfileAPI } from '@/src/networks/profile/userProfile';
import { fetchPresignedUrlAPI } from '@/src/networks/storage/presignedUrl';
import type { MediaPreviewItem, MessageI, ProfileData } from './types';

export const useChatScreenHook = (profileId: string) => {
  const [messages, setMessages] = useState<MessageI[]>([]);
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalMessages, setTotalMessages] = useState(0);
  const [replyPreview, setReplyPreview] = useState<MessageI | null>(null);
  const [selectedMessage, setSelectedMessage] = useState<MessageI | null>(null);
  const [messageText, setMessageText] = useState('');
  const [sending, setSending] = useState(false);
  const [pendingMessages, setPendingMessages] = useState<Map<string, MessageI>>(new Map());
  const [isUserOnline, setIsUserOnline] = useState(false);
  const [selectedMessages, setSelectedMessages] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [deleteOptionsVisible, setDeleteOptionsVisible] = useState(false);
  const [clearChatOptions, setClearChatOptionsVisible] = useState(false);
  const [clearChatVisible, setClearChatVisible] = useState(false);
  const [lastSeen, setLastSeen] = useState<Date | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [audioRecording, setAudioRecording] = useState<any>(null);
  const [audioDurations, setAudioDurations] = useState<{ [key: string]: number }>({});
  const [linkPreviews, setLinkPreviews] = useState<{ [url: string]: any }>({});

  const currentUser = useSelector(selectCurrentUser);
  const flatListRef = useRef<any>(null);
  const PAGE_SIZE = 10;

  const { sendMessage: socketSend, onMessage, removeMessageHandler, isConnected } = useSocket();

  const defaultUserProfile: ProfileData = {
    name: 'User',
    profileId,
    email: '',
    username: '',
    avatar: null,
    designation: null,
    entity: null,
  };

  const currentUserProfile: ProfileData = {
    name: 'You',
    profileId: currentUser.profileId,
    email: '',
    username: '',
    avatar: null,
    designation: null,
    entity: null,
  };

  const getAudioDuration = async (audioUrl: string): Promise<number> => {
    return new Promise((resolve) => {
      const tempPlayer = new AudioRecorderPlayer();

      tempPlayer
        .startPlayer(audioUrl)
        .then(() => {
          tempPlayer.addPlayBackListener((e) => {
            if (e.duration > 0) {
              tempPlayer.stopPlayer();
              tempPlayer.removePlayBackListener();
              resolve(e.duration);
            }
          });
        })
        .catch(() => {
          resolve(0);
        });

      setTimeout(() => {
        tempPlayer.stopPlayer();
        tempPlayer.removePlayBackListener();
        resolve(0);
      }, 5000);
    });
  };

  const loadAudioDuration = async (messageId: string, audioUrl: string) => {
    try {
      const duration = await getAudioDuration(audioUrl);
      setAudioDurations((prev) => ({
        ...prev,
        [messageId]: duration,
      }));
    } catch (error) {}
  };

  const fetchLinkPreview = async (url: string) => {
    if (linkPreviews[url]) return linkPreviews[url];

    try {
      const response = await fetch(url);
      const html = await response.text();

      const getMetaTag = (name: string): string | undefined => {
        const match = html.match(
          new RegExp(
            `<meta(?:.*?)(?:property|name)=["']${name}["'](?:.*?)content=["'](.*?)["']`,
            'i',
          ),
        );
        return match ? match[1] : undefined;
      };

      const title = getMetaTag('og:title') || getMetaTag('twitter:title') || getMetaTag('title');
      const description =
        getMetaTag('og:description') ||
        getMetaTag('twitter:description') ||
        getMetaTag('description');
      const image = getMetaTag('og:image') || getMetaTag('twitter:image');
      const siteName = getMetaTag('og:site_name') || new URL(url).hostname;

      if (title || image) {
        const preview = {
          title,
          description,
          image,
          siteName,
        };

        setLinkPreviews((prev) => ({
          ...prev,
          [url]: preview,
        }));

        return preview;
      }
    } catch (error) {}

    return null;
  };

  const normalizeMediaMimeType = (mimeType: string): string => {
    const normalizedType = mimeType.toLowerCase();

    if (normalizedType.startsWith('image/')) {
      return 'JPEG';
    } else if (normalizedType === 'application/pdf' || normalizedType.includes('pdf')) {
      return 'PDF';
    } else if (normalizedType === 'text/plain' || normalizedType.includes('txt')) {
      return 'TEXT';
    } else if (
      normalizedType === 'audio/mpeg' ||
      normalizedType.includes('mp3') ||
      normalizedType === 'audio/mp3'
    ) {
      return 'MP3';
    } else if (normalizedType === 'video/mp4' || normalizedType.includes('mp4')) {
      return 'MP4';
    }

    return 'JPEG';
  };

  const loadMessages = async (page = 0, isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else if (page > 0) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      const response = await findAllSpecificProfileChats({
        profileId,
        page,
        pageSize: PAGE_SIZE,
      });

      const messagesWithUser: MessageI[] = response.data.map((msg: MessageData) => ({
        ...msg,
        user:
          msg.senderId === currentUser.profileId
            ? currentUserProfile
            : profile || defaultUserProfile,
      }));

      messagesWithUser.forEach((message) => {
        if (message.content.media) {
          message.content.media.forEach((media: any) => {
            if (
              media.mimeType === 'MP3' ||
              media.mimeType.includes('mp3') ||
              media.mimeType === 'audio/mpeg'
            ) {
              loadAudioDuration(message.id, media.url);
            }
          });
        }

        if (message.content.text) {
          const urlRegex = /(https?:\/\/[^\s]+)/g;
          const urls = message.content.text.match(urlRegex);
          if (urls) {
            urls.forEach((url) => {
              fetchLinkPreview(url);
            });
          }
        }
      });

      if (isRefresh) {
        setMessages(messagesWithUser);
        setCurrentPage(0);
      } else if (page > 0) {
        setMessages((prev) => [...prev, ...messagesWithUser]);
        setCurrentPage(page);
      } else {
        setMessages(messagesWithUser);
        setCurrentPage(0);
      }

      setTotalMessages(response.total);
      setHasMore(response.hasMore);
    } catch (err) {
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setRefreshing(false);
    }
  };

  const refreshMessages = () => {
    loadMessages(0, true);
  };

  const loadProfile = async () => {
    try {
      const profileData = await fetchProfileAPI(profileId);
      setProfile(profileData);
    } catch (err) {}
  };

  const handleLoadMore = () => {
    if (!loadingMore && hasMore && !refreshing) {
      loadMessages(currentPage + 1);
    }
  };

  const uploadMedia = async (selectedMedia: any[]): Promise<MediaPreviewItem[]> => {
    const extensions = selectedMedia
      .map((file) => {
        const mimeType = file.mime || file.type || '';
        if (mimeType.startsWith('image/')) {
          return mimeType.split('/')[1];
        } else if (mimeType === 'audio/mpeg' || mimeType.includes('mp3')) {
          return 'mp3';
        } else if (mimeType === 'video/mp4' || mimeType.includes('mp4')) {
          return 'mp4';
        } else if (mimeType === 'application/pdf' || mimeType.includes('pdf')) {
          return 'pdf';
        }
        return mimeType.split('/')[1];
      })
      .filter((ext) => ext);

    if (extensions.length === 0) {
      throw new Error('No valid media files');
    }

    const response = await fetchPresignedUrlAPI(extensions, 'CHAT');

    if (!Array.isArray(response) || response.length !== selectedMedia.length) {
      throw new Error('Failed to get upload URLs');
    }

    await Promise.all(
      selectedMedia.map((file, index) => {
        const presignedData = response[index];
        const fileBlob = {
          uri: file.path || file.uri,
          type: file.mime || file.type,
          filename: file.filename || `media_${index}.${extensions[index]}`,
        };
        return uploadFileWithPresignedUrl(fileBlob, presignedData.uploadUrl);
      }),
    );

    return response.map((item, index) => ({
      url: item.accessUrl,
      mimeType: selectedMedia[index].mime || selectedMedia[index].type,
      name: selectedMedia[index].filename || `media_${index}.${extensions[index]}`,
    }));
  };

  const sendMessage = async () => {
    if (!messageText.trim() || sending || !isConnected) return;

    const tempId = `temp_${Date.now()}`;
    const currentMessageText = messageText.trim();
    const currentReplyTo = replyPreview?.id || null;

    try {
      setSending(true);

      const tempMessage: MessageI = {
        id: tempId,
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: { text: currentMessageText, media: [] },
        messageType: 'TEXT',
        replyTo: currentReplyTo,
        createdAt: new Date(),
        deletedForAll: false,
        deletedFor: [],
        user: currentUserProfile,
      };

      setMessages((prev) => [...prev, tempMessage]);
      setPendingMessages((prev) => new Map(prev.set(tempId, tempMessage)));
      setMessageText('');
      setReplyPreview(null);

      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);

      const urlRegex = /(https?:\/\/[^\s]+)/g;
      const urls = currentMessageText.match(urlRegex);
      if (urls) {
        urls.forEach((url) => {
          fetchLinkPreview(url);
        });
      }

      socketSend('individual', {
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: {
          text: currentMessageText,
          media: [],
        },
        replyTo: currentReplyTo,
        profileId: currentUser.profileId,
      });
    } catch (err) {
      setMessages((prev) => prev.filter((msg) => msg.id !== tempId));
      setPendingMessages((prev) => {
        const newMap = new Map(prev);
        newMap.delete(tempId);
        return newMap;
      });
    } finally {
      setSending(false);
    }
  };

  const sendMediaMessage = async (mediaItems: MediaPreviewItem[], captionText?: string) => {
    if (!isConnected || sending || mediaItems.length === 0) {
      return;
    }

    const tempId = `temp_${Date.now()}`;
    const currentReplyTo = replyPreview?.id || null;

    try {
      setSending(true);
      const tempMessage: MessageI = {
        id: tempId,
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: {
          text: captionText || null,
          media: mediaItems,
        },
        messageType: 'MEDIA',
        replyTo: currentReplyTo,
        createdAt: new Date(),
        deletedForAll: false,
        deletedFor: [],
        user: currentUserProfile,
      };
      setMessages((prev) => [...prev, tempMessage]);
      setPendingMessages((prev) => new Map(prev.set(tempId, tempMessage)));
      if (replyPreview) {
        setReplyPreview(null);
      }
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);

      if (captionText) {
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        const urls = captionText.match(urlRegex);
        if (urls) {
          urls.forEach((url) => {
            fetchLinkPreview(url);
          });
        }
      }

      const socketPayload = {
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: {
          text: captionText || null,
          media: mediaItems.map((item) => ({
            ...item,
            mimeType: normalizeMediaMimeType(item.mimeType),
          })),
        },
        replyTo: currentReplyTo,
        profileId: currentUser.profileId,
      };
      socketSend('individual', socketPayload);
    } catch (err) {
      setMessages((prev) => prev.filter((msg) => msg.id !== tempId));
      setPendingMessages((prev) => {
        const newMap = new Map(prev);
        newMap.delete(tempId);
        return newMap;
      });
    } finally {
      setSending(false);
    }
  };

  const sendAudioMessage = async (audioFile: any, caption?: string) => {
    if (!audioFile || !isConnected) return;

    try {
      const mediaItems = await uploadMedia([audioFile]);
      await sendMediaMessage(mediaItems, caption || messageText.trim());
    } catch (err) {
    } finally {
      setAudioRecording(null);
    }
  };

  const startRecording = () => {
    setIsRecording(true);
  };

  const stopRecording = (audioFile: any) => {
    setIsRecording(false);
    setAudioRecording(audioFile);
    if (audioFile) {
      sendAudioMessage(audioFile);
    }
  };

  const editMessage = async (messageId: string, newContent: string) => {
    try {
      const message = messages.find((msg) => msg.id === messageId);
      if (!message) return;

      const updatedContent = {
        text: newContent,
        media: message.content.media || [],
      };

      socketSend('edit-message', {
        id: messageId,
        senderId: currentUser.profileId,
        recieverId: profileId,
        content: updatedContent,
        profileId: currentUser.profileId,
      });

      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === messageId ? { ...msg, content: updatedContent, editedAt: new Date() } : msg,
        ),
      );

      const urlRegex = /(https?:\/\/[^\s]+)/g;
      const urls = newContent.match(urlRegex);
      if (urls) {
        urls.forEach((url) => {
          fetchLinkPreview(url);
        });
      }
    } catch (err) {}
  };

  const deleteManyForMe = async () => {
    const messageIds = Array.from(selectedMessages);
    if (messageIds.length === 0) return;
    try {
      socketSend('delete-for-me', {
        ids: messageIds,
        senderId: currentUser.profileId,
        recieverId: profileId,
        profileId: currentUser.profileId,
        type: 'FOR_ME',
      });

      setMessages((prev) => prev.filter((msg) => !messageIds.includes(msg.id)));
      setSelectedMessages(new Set());
      setIsSelectionMode(false);
    } catch (err) {}
  };

  const deleteManyForEveryone = async () => {
    const messageIds = Array.from(selectedMessages);
    if (messageIds.length === 0) return;
    try {
      socketSend('delete-for-everyone', {
        ids: messageIds,
        senderId: currentUser.profileId,
        recieverId: profileId,
        profileId: currentUser.profileId,
        type: 'FOR_EVERYONE',
      });

      setMessages((prev) =>
        prev.map((msg) =>
          messageIds.includes(msg.id)
            ? {
                ...msg,
                deletedForAll: true,
                content: { ...msg.content, text: 'This message has been deleted' },
              }
            : msg,
        ),
      );
      setSelectedMessages(new Set());
      setIsSelectionMode(false);
    } catch (err) {}
  };

  const clearChat = async () => {
    try {
      await deleteSpecificProfileChat(profileId);
      setMessages([]);
      setClearChatVisible(false);
    } catch (err) {}
  };

  const handleMessageLongPress = (message: MessageI) => {
    if (isSelectionMode) {
      toggleMessageSelection(message.id);
    } else {
      setSelectedMessage(message);
    }
  };

  const handleMessagePress = (message: MessageI) => {
    if (isSelectionMode) {
      toggleMessageSelection(message.id);
    }
  };

  const toggleMessageSelection = (messageId: string) => {
    setSelectedMessages((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(messageId)) {
        newSet.delete(messageId);
      } else {
        newSet.add(messageId);
      }

      if (newSet.size === 0) {
        setIsSelectionMode(false);
      }

      return newSet;
    });
  };

  const startSelectionMode = (messageId: string) => {
    setIsSelectionMode(true);
    setSelectedMessages(new Set([messageId]));
  };

  const exitSelectionMode = () => {
    setIsSelectionMode(false);
    setSelectedMessages(new Set());
  };

  const handleDeleteSelected = () => {
    if (selectedMessages.size > 0) {
      setDeleteOptionsVisible(true);
    }
  };

  const handleDeleteSelectedForMe = () => {
    const messageIds = Array.from(selectedMessages);
    if (messageIds.length === 0) return;
    deleteManyForMe();
    setDeleteOptionsVisible(false);
    setIsSelectionMode(false);
  };

  const handleDeleteSelectedForEveryone = () => {
    const messageIds = Array.from(selectedMessages);
    if (messageIds.length === 0) return;
    const canDeleteForEveryone = messageIds.every((id) => {
      const message = messages.find((msg) => msg.id === id);
      return (
        message && message.senderId === currentUser.profileId && canDeleteForEveryoneCheck(message)
      );
    });

    if (canDeleteForEveryone) {
      deleteManyForEveryone();
    }
    setDeleteOptionsVisible(false);
    setIsSelectionMode(false);
  };

  const handleClearChat = () => {
    setClearChatOptionsVisible(false);
  };

  const handleClearChatOptions = () => {
    setClearChatOptionsVisible(true);
  };

  const handleConfirmClearChat = () => {
    clearChat();
  };

  const canDeleteForEveryoneCheck = (message: MessageI): boolean => {
    if (message.senderId !== currentUser.profileId || message.deletedForAll) return false;
    const deleteTimeLimit = new Date(message.createdAt);
    deleteTimeLimit.setMinutes(deleteTimeLimit.getMinutes() + 30);
    return new Date() <= deleteTimeLimit;
  };

  const canDeleteSelectedForEveryone = (): boolean => {
    return Array.from(selectedMessages).every((id) => {
      const message = messages.find((msg) => msg.id === id);
      return (
        message && message.senderId === currentUser.profileId && canDeleteForEveryoneCheck(message)
      );
    });
  };

  const handleReply = () => {
    if (selectedMessage) {
      setReplyPreview(selectedMessage);
    }
    setSelectedMessage(null);
  };

  const handleSwipeReply = (message: MessageI) => {
    setReplyPreview(message);
  };

  const handleCloseOptions = () => {
    setSelectedMessage(null);
  };

  const handleCloseReply = () => {
    setReplyPreview(null);
  };

  const formatAudioTime = (milliseconds: number) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  interface SocketHandlers {
    handleIndividualMessage: (data: any) => void;
    handleMessageError: (data: any) => void;
    handleMessagesDeleted: (data: { ids: string[]; senderId: string }) => void;
    handleMessageDeleted: (data: { id: string; senderId: string }) => void;
    handleMessageEdited: (data: { id: string; content: any; senderId: string }) => void;
    handleUserStatus: (data: { profileId: string; status: string; lastSeen: string }) => void;
  }

  const socketHandlers: SocketHandlers = {
    handleIndividualMessage: (data: any) => {
      const messageWithUser: MessageI = {
        ...data,
        user:
          data.senderId === currentUser.profileId
            ? currentUserProfile
            : profile || defaultUserProfile,
      };

      if (messageWithUser.content.media) {
        messageWithUser.content.media.forEach((media: any) => {
          if (
            media.mimeType === 'MP3' ||
            media.mimeType.includes('mp3') ||
            media.mimeType === 'audio/mpeg'
          ) {
            loadAudioDuration(messageWithUser.id, media.url);
          }
        });
      }

      if (messageWithUser.content.text) {
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        const urls = messageWithUser.content.text.match(urlRegex);
        if (urls) {
          urls.forEach((url) => {
            fetchLinkPreview(url);
          });
        }
      }

      const tempMessage = Array.from(pendingMessages.values()).find(
        (msg) =>
          msg.senderId === data.senderId &&
          msg.recieverId === data.recieverId &&
          (msg.content.text === data.content.text || (msg.content.media && data.content.media)),
      );

      if (tempMessage) {
        setMessages((prev) =>
          prev.map((msg) => (msg.id === tempMessage.id ? messageWithUser : msg)),
        );
        setPendingMessages((prev) => {
          const newMap = new Map(prev);
          newMap.delete(tempMessage.id);
          return newMap;
        });
      } else {
        setMessages((prev) => [...prev, messageWithUser]);
      }

      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    },

    handleMessageError: (data: any) => {
      const failedMessage = Array.from(pendingMessages.values()).find(
        (msg) =>
          msg.senderId === data.originalSenderId && msg.recieverId === data.originalRecieverId,
      );

      if (failedMessage) {
        setMessages((prev) => prev.filter((msg) => msg.id !== failedMessage.id));
        setPendingMessages((prev) => {
          const newMap = new Map(prev);
          newMap.delete(failedMessage.id);
          return newMap;
        });
      }
    },

    handleMessagesDeleted: (data: { ids: string[]; senderId: string }) => {
      setMessages((prev) => prev.filter((msg) => !data.ids.includes(msg.id)));
    },

    handleMessageDeleted: (data: { id: string; senderId: string }) => {
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === data.id
            ? {
                ...msg,
                deletedForAll: true,
                content: { ...msg.content, text: 'This message has been deleted' },
              }
            : msg,
        ),
      );
    },

    handleMessageEdited: (data: { id: string; content: any; senderId: string }) => {
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === data.id ? { ...msg, content: data.content, editedAt: new Date() } : msg,
        ),
      );

      if (data.content.text) {
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        const urls = data.content.text.match(urlRegex);
        if (urls) {
          urls.forEach((url: string) => {
            fetchLinkPreview(url);
          });
        }
      }
    },

    handleUserStatus: (data: { profileId: string; status: string; lastSeen: string }) => {
      if (data.profileId !== profileId) return;
      setIsUserOnline(data.status === 'online');
      setLastSeen(data.status === 'offline' ? new Date(data.lastSeen) : null);
    },
  };

  useEffect(() => {
    const initializeChat = async () => {
      try {
        await Promise.all([loadProfile(), loadMessages(0)]);
      } catch (error) {
        setLoading(false);
      }
    };

    initializeChat();
  }, [profileId]);

  useEffect(() => {
    if (!isConnected) return;

    const handlers = socketHandlers;

    onMessage('individual', handlers.handleIndividualMessage);
    onMessage('message-error', handlers.handleMessageError);
    onMessage('messages-deleted', handlers.handleMessagesDeleted);
    onMessage('message-deleted', handlers.handleMessageDeleted);
    onMessage('message-edited', handlers.handleMessageEdited);
    onMessage('user-status', handlers.handleUserStatus);

    return () => {
      removeMessageHandler('individual');
      removeMessageHandler('message-error');
      removeMessageHandler('messages-deleted');
      removeMessageHandler('message-deleted');
      removeMessageHandler('message-edited');
      removeMessageHandler('messages-read');
      removeMessageHandler('read-receipt');
      removeMessageHandler('user-status');
    };
  }, [isConnected, pendingMessages, profile]);

  return {
    messages,
    profile,
    loading,
    loadingMore,
    refreshing,
    hasMore,
    totalMessages,
    currentPage,
    replyPreview,
    selectedMessage,
    messageText,
    sending,
    flatListRef,
    isConnected,
    isUserOnline,
    lastSeen,
    selectedMessages,
    isSelectionMode,
    deleteOptionsVisible,
    clearChatVisible,
    isRecording,
    audioRecording,
    audioDurations,
    setMessageText,
    setSelectedMessage,
    setDeleteOptionsVisible,
    setClearChatVisible,
    sendMessage,
    sendMediaMessage,
    uploadMedia,
    editMessage,
    handleDeleteSelectedForEveryone,
    handleDeleteSelectedForMe,
    clearChat,
    handleLoadMore,
    refreshMessages,
    handleMessageLongPress,
    handleMessagePress,
    handleReply,
    handleSwipeReply,
    handleCloseOptions,
    handleCloseReply,
    startSelectionMode,
    exitSelectionMode,
    handleDeleteSelected,
    handleClearChat,
    handleClearChatOptions,
    handleConfirmClearChat,
    canDeleteSelectedForEveryone,
    clearChatOptions,
    setClearChatOptionsVisible,
    startRecording,
    stopRecording,
    formatAudioTime,
    loadAudioDuration,
    fetchLinkPreview,
    linkPreviews,
    setMessages,
    setReplyPreview,
  };
};
