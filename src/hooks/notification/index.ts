import { useRef } from 'react';
import { Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import notifee, {
  AndroidImportance,
  EventType,
  type Notification,
  type AuthorizationStatus,
} from '@notifee/react-native';
import { getApp } from '@react-native-firebase/app';
import {
  getMessaging,
  getToken,
  onMessage,
  setBackgroundMessageHandler,
  onNotificationOpenedApp,
  getInitialNotification,
  type FirebaseMessagingTypes,
} from '@react-native-firebase/messaging';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import FirebaseService from '@/src/services/firebase';
import useStorage from '@/src/hooks/storage';
import type {
  NotificationChannel,
  LocalNotificationOptions,
  NotificationHookReturn,
  NotificationData,
  NotificationSettings,
} from './types';

const useNotification = (): NotificationHookReturn => {
  const navigation = useNavigation<BottomTabNavigationI>();
  const currentUser = useSelector(selectCurrentUser);
  const messagingRef = useRef<FirebaseMessagingTypes.Module | null>(null);
  const { getStorage, setStorage } = useStorage();

  const initFirebaseIfNeeded = async (): Promise<void> => {
    await FirebaseService.getInstance();
    const app = getApp();
    messagingRef.current = getMessaging(app);
  };

  const createNotificationChannels = async (
    channels: NotificationChannel[] = [],
  ): Promise<void> => {
    const defaultChannels: NotificationChannel[] = [
      {
        id: 'default',
        name: 'Default Notifications',
        description: 'General app notifications',
        importance: AndroidImportance.DEFAULT,
      },
      {
        id: 'high_priority',
        name: 'Important Notifications',
        description: 'High priority notifications',
        importance: AndroidImportance.HIGH,
      },
      ...channels,
    ];

    await Promise.all(
      defaultChannels.map((channel) =>
        notifee.createChannel({
          id: channel.id,
          name: channel.name,
          description: channel.description,
          importance: channel.importance || AndroidImportance.DEFAULT,
          vibration: channel.vibration !== false,
          vibrationPattern: channel.vibrationPattern,
          lights: channel.lights !== false,
          lightColor: channel.lightColor,
        }),
      ),
    );
  };

  const checkNotificationPermissions = async (): Promise<boolean> => {
    const settings = await notifee.getNotificationSettings();
    return settings.authorizationStatus >= 1;
  };

  const getDeviceToken = async (forceRefresh = false): Promise<string | null> => {
    try {
      let deviceToken = forceRefresh ? null : await getStorage('deviceToken');
      if (deviceToken && !forceRefresh) return deviceToken;

      if (!messagingRef.current) await initFirebaseIfNeeded();

      const hasPermission = await checkNotificationPermissions();
      if (!hasPermission) return null;

      if (Platform.OS === 'ios' && !messagingRef.current!.isDeviceRegisteredForRemoteMessages) {
        await messagingRef.current!.registerDeviceForRemoteMessages();
      }

      deviceToken = await getToken(messagingRef.current!);
      if (deviceToken) await setStorage('deviceToken', deviceToken);

      return deviceToken;
    } catch (error) {
      return null;
    }
  };

  const deleteDeviceToken = async (): Promise<boolean> => {
    try {
      await setStorage('deviceToken', '');
      return true;
    } catch {
      return false;
    }
  };

  const handleNotificationPress = (data: NotificationData): void => {
    if (!data) return;

    if (data.commentId || data.parentCommentId || data.postId) {
      navigation.navigate('HomeStack', {
        screen: 'Comment',
        params: {
          postId: data.postId!,
          type: 'USER_POST',
        },
      });
    } else if (data.actorProfileId) {
      const profileParams = {
        profileId: data.actorProfileId,
        fromTabPress: false,
      };

      const isCurrentUser = currentUser.profileId === data.actorProfileId;
      const route = isCurrentUser ? 'UserProfile' : 'OtherUserProfile';

      navigation.navigate('ProfileStack', {
        screen: route,
        params: profileParams,
      });
    }
  };

  const displayNotification = async (options: LocalNotificationOptions): Promise<string> => {
    const notification: Notification = {
      title: options.title,
      body: options.body,
      data: options.data || {},
    };

    if (Platform.OS === 'android') {
      notification.android = {
        channelId: options.android?.channelId || 'default',
        importance: options.android?.importance || AndroidImportance.HIGH,
        smallIcon: options.android?.smallIcon || 'ic_notification',
        pressAction: {
          id: 'default',
          launchActivity: 'default',
        },
      };
    }

    if (Platform.OS === 'ios') {
      notification.ios = {
        sound: 'default',
        attachments: [],
      };
    }

    return await notifee.displayNotification(notification);
  };

  const scheduleNotification = async (options: LocalNotificationOptions): Promise<string> => {
    const notification: Notification = {
      title: options.title,
      body: options.body,
      data: options.data || {},
    };

    if (Platform.OS === 'android') {
      notification.android = {
        channelId: 'default',
        importance: AndroidImportance.HIGH,
      };
    }

    if (!options.scheduleDate) {
      return await notifee.displayNotification(notification);
    }

    const trigger = {
      type: 0 as const,
      timestamp: options.scheduleDate.getTime(),
    };

    return await notifee.createTriggerNotification(notification, trigger);
  };

  const cancelNotification = async (notificationId: string): Promise<void> => {
    await notifee.cancelNotification(notificationId);
  };

  const cancelAllNotifications = async (): Promise<void> => {
    await notifee.cancelAllNotifications();
  };

  const getNotificationSettings = async (): Promise<NotificationSettings> => {
    const settings = await notifee.getNotificationSettings();
    return {
      authorizationStatus: settings.authorizationStatus as AuthorizationStatus,
      alert: settings.ios?.alert === 1,
      badge: settings.ios?.badge === 1,
      sound: settings.ios?.sound === 1,
      carPlay: settings.ios?.carPlay === 1,
      lockScreen: settings.ios?.lockScreen === 1,
      notificationCenter: settings.ios?.notificationCenter === 1,
      criticalAlert: settings.ios?.criticalAlert === 1,
      announcement: settings.ios?.announcement === 1,
    };
  };

  const setupNotification = async (channels?: NotificationChannel[]): Promise<boolean> => {
    try {
      await initFirebaseIfNeeded();
      await createNotificationChannels(channels);

      if (!messagingRef.current) return false;

      onMessage(messagingRef.current, async (remoteMessage) => {
        const { notification, data } = remoteMessage;

        if (notification) {
          await displayNotification({
            title: notification.title || 'New Notification',
            body: notification.body || 'You have a new message',
            data: data || {},
          });
        }
      });

      setBackgroundMessageHandler(messagingRef.current, async (remoteMessage) => {
        const { notification, data } = remoteMessage;
        if (notification) {
          await displayNotification({
            title: notification.title || 'New Notification',
            body: notification.body || 'You have a new message',
            data: data || {},
          });
        }
        return Promise.resolve();
      });

      onNotificationOpenedApp(messagingRef.current, (remoteMessage) => {
        handleNotificationPress(remoteMessage.data as NotificationData);
      });

      getInitialNotification(messagingRef.current).then((remoteMessage) => {
        if (remoteMessage) {
          handleNotificationPress(remoteMessage.data as NotificationData);
        }
      });

      notifee.onForegroundEvent(({ type, detail }) => {
        if (type === EventType.PRESS && detail.notification?.data) {
          handleNotificationPress(detail.notification.data as NotificationData);
        }
      });

      return true;
    } catch (error) {
      console.error('Error setting up notifications:', error);
      return false;
    }
  };

  return {
    setupNotification,
    getDeviceToken,
    checkNotificationPermissions,
    displayNotification,
    scheduleNotification,
    cancelNotification,
    cancelAllNotifications,
    createNotificationChannels,
    getNotificationSettings,
    deleteDeviceToken,
    handleNotificationPress,
  };
};

export default useNotification;
