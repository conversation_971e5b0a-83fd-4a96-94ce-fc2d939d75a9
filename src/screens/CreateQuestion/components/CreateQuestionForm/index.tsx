import {
  View,
  Text,
  Pressable,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Image,
  ActivityIndicator,
} from 'react-native';
import { Controller } from 'react-hook-form';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import TextInput from '@/src/components/TextInput';
import Button from '@/src/components/Button';
import Close from '@/src/assets/svgs/Close';
import Attachment from '@/src/assets/svgs/Attachment';
import TrashBin from '@/src/assets/svgs/TrashBin';
import type { CreateQuestionFormProps, MediaI } from './types';
import useCreateQuestionForm from './useHook';

const CreateQuestionForm = ({
  onSuccess,
  editing,
  questionId,
}: CreateQuestionFormProps) => {
  const navigation = useNavigation();
  const {
    control,
    errors,
    isSubmitting,
    isLoading,
    attachments,
    titleInputRef,
    descriptionInputRef,
    handleSubmit,
    handleAttachments,
    handleDeleteAttachment,
    focusTitleInput,
    isFormValid,
    watchedValues,
  } = useCreateQuestionForm({ onSuccess, editing, questionId });

  const handleClose = () => {
    navigation.goBack();
  };

  useFocusEffect(() => {
    const timer = setTimeout(
      () => {
        focusTitleInput();
      },
      Platform.OS === 'android' ? 400 : 100,
    );

    return () => clearTimeout(timer);
  });

  const renderAttachmentThumbnail = (attachment: MediaI, index: number) => {
    const isImage = attachment.type.startsWith('image/');
    const isPdf = attachment.type === 'application/pdf';
    const isText = attachment.type === 'text/plain';

    return (
      <View key={`attachment-${index}`} className="flex-row items-center p-3 bg-gray-50 rounded-lg mb-2">
        <View className="mr-3">
          {isImage ? (
            <Image
              source={{ uri: attachment.uri }}
              style={{ width: 40, height: 40, borderRadius: 4 }}
              resizeMode="cover"
            />
          ) : (
            <View className="w-10 h-10 bg-gray-200 rounded items-center justify-center">
              <Text className="text-xs font-medium text-gray-600">
                {isPdf ? 'PDF' : isText ? 'TXT' : 'FILE'}
              </Text>
            </View>
          )}
        </View>
        <View className="flex-1">
          <Text className="text-sm font-medium text-gray-900" numberOfLines={1}>
            {attachment.filename}
          </Text>
          <Text className="text-xs text-gray-500">
            {attachment.type}
          </Text>
        </View>
        <Pressable
          onPress={() => handleDeleteAttachment(index)}
          className="p-2"
          hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
        >
          <TrashBin color="#EF4444" width={2} height={2} />
        </Pressable>
      </View>
    );
  };

  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center bg-white">
        <ActivityIndicator size="large" color="#22C55E" />
        <Text className="mt-2 text-gray-600">Loading question...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      className="flex-1"
      contentContainerStyle={{ flexGrow: 1, padding: 16 }}
      keyboardShouldPersistTaps="handled"
      showsVerticalScrollIndicator={false}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        className="flex-1"
      >
        <View className="flex-1 bg-white">
          <View className="flex-row justify-between items-center  py-3 border-b border-gray-100">
            <View className="flex-row items-center">
              <Pressable
                onPress={handleClose}
                className="rounded-full active:bg-gray-100 p-1"
              >
                <Close height={2.5} width={2.5} />
              </Pressable>
              <Text className="text-lg font-semibold ml-2 text-gray-900">
                2/2
              </Text>
            </View>
            <View className="flex-row gap-2 items-center">
              <Pressable onPress={handleAttachments} className="p-2">
                <Attachment />
              </Pressable>
              <Button
                className="w-auto px-4 rounded-full"
                label="Send request"
                onPress={handleSubmit}
                loading={isSubmitting}
                disabled={isSubmitting || !isFormValid}
              />
            </View>
          </View>


          <View className="mb-6">
            <Text className="text-base font-medium text-gray-900 mb-2">
              Title
            </Text>
            <Controller
              control={control}
              name="title"
              render={({ field: { onChange, value } }) => (
                <TextInput
                  inputRef={titleInputRef}
                  placeholder="Enter question title"
                  value={value}
                  onChangeText={onChange}
                  error={errors.title?.message}
                  className="mb-0"
                  maxLength={200}
                  returnKeyType="next"
                />
              )}
            />
          </View>

          <View className="mb-6">
            <Text className="text-base font-medium text-gray-900 mb-2">
              Description
            </Text>
            <Controller
              control={control}
              name="description"
              render={({ field: { onChange, value } }) => (
                <TextInput
                  inputRef={descriptionInputRef}
                  type="textarea"
                  placeholder="Enter question description"
                  value={value}
                  onChangeText={onChange}
                  error={errors.description?.message}
                  className="mb-0"
                  maxLength={2000}
                  numberOfLines={8}
                />
              )}
            />
          </View>

          {attachments.length > 0 && (
            <View className="mb-6">
              <Text className="text-base font-medium text-gray-900 mb-3">
                Attachments ({attachments.length}/5)
              </Text>
              {attachments.map((attachment, index) =>
                renderAttachmentThumbnail(attachment, index),
              )}
            </View>
          )}

        </View>
      </KeyboardAvoidingView>
    </ScrollView>

  );
};

export default CreateQuestionForm;
