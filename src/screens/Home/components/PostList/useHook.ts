import { useCallback, useState, useEffect } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import NetInfo from '@react-native-community/netinfo';
import { useDispatch, useSelector } from 'react-redux';
import { selectAllPosts, selectPagination, selectCachedPosts } from '@/src/redux/selectors/content';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import {
  addReactionOptimistc,
  fetchPosts,
  removeReactionOptimistic,
  deletePostOptimistic,
  revertDeletePostOptimistic,
} from '@/src/redux/slices/content/contentSlice';
import type { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';
import { deletePostAPI } from '@/src/networks/content/post';
import { upsertReactionAPI, deleteReactionAPI } from '@/src/networks/content/reaction';
import type { PostExternalClientI } from '@/src/networks/content/types';
import type { UsePostListResult } from './types';

const usePostList = (): UsePostListResult => {
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isConnected, setIsConnected] = useState<boolean | null>(true);
  const dispatch = useDispatch<AppDispatch>();

  const posts = useSelector(selectAllPosts);
  const cachedPosts = useSelector(selectCachedPosts);
  const pagination = useSelector(selectPagination);
  const currentUser = useSelector(selectCurrentUser);

  const displayPosts = posts.length ? posts : cachedPosts;

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      setIsConnected(state.isConnected);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const fetchLatestPosts = async () => {
    if (!isConnected) {
      return;
    }

    setLoading(true);
    try {
      await dispatch(fetchPosts({ refresh: true })).unwrap();
    } catch (error) {
      setLoading(false);
      const errorMessage = `Failed to fetch posts: ${error instanceof APIResError ? error.message : 'Unknown error'}`;
      triggerErrorBoundary(new Error(errorMessage));
    } finally {
      setLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchLatestPosts();
    }, [isConnected]),
  );

  const handleRefresh = async () => {
    if (refreshing) return;

    if (!isConnected) {
      return;
    }

    setRefreshing(true);
    try {
      await dispatch(fetchPosts({ refresh: true })).unwrap();
    } catch (error) {
      if (!posts.length) {
        const errorMessage = `Failed to refresh posts: ${error instanceof APIResError ? error.message : 'Unknown error'}`;
        triggerErrorBoundary(new Error(errorMessage));
      } else {
        showToast({ message: 'Failed to refresh posts', type: 'error' });
      }
    } finally {
      setRefreshing(false);
    }
  };

  const handleLoadMore = async () => {
    if (!pagination.hasMore || loading || refreshing || posts.length === 0 || !isConnected) {
      return;
    }
    setLoading(true);
    try {
      await dispatch(fetchPosts({})).unwrap();
    } catch (error) {
      showToast({ message: 'Failed to load more posts', type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleLikePost = async (post: PostExternalClientI, reactionType = 'LIKE') => {
    if (!currentUser) return;
    if (!isConnected) {
      return;
    }

    try {
      if (post.isLiked) {
        dispatch(removeReactionOptimistic({ postId: post.id }));
        await deleteReactionAPI({ postId: post.id });
      } else {
        dispatch(
          addReactionOptimistc({
            postId: post.id,
          }),
        );
        await upsertReactionAPI({
          postId: post.id,
          reactionType,
        });
      }
    } catch (error) {
      if (post.isLiked) {
        dispatch(
          addReactionOptimistc({
            postId: post.id,
          }),
        );
      } else {
        dispatch(removeReactionOptimistic({ postId: post.id }));
      }
      showToast({ message: 'Failed to update like', type: 'error' });
    }
  };

  const handleDeletePost = async (post: PostExternalClientI) => {
    if (!isConnected) {
      return;
    }

    try {
      dispatch(deletePostOptimistic({ post }));
      await deletePostAPI(post.id);
    } catch (error) {
      dispatch(revertDeletePostOptimistic({ postId: post.id }));
      showToast({ message: 'Failed to delete post', type: 'error' });
    }
  };

  return {
    posts: displayPosts,
    refreshing,
    loading,
    handleRefresh,
    handleLoadMore,
    handleLikePost,
    handleDeletePost,
  };
};

export default usePostList;
