import { Platform } from 'react-native';
import Config from 'react-native-config';
import { resetUserState } from '@/src/redux/slices/user/userSlice';
import { store } from '@/src/redux/store';
import { isFilled, omit } from '@/src/utilities/data/object';
import { generateUrl } from '@/src/utilities/networks/api';
import APIResError from '@/src/errors/networks/APIResError';
import AppError from '@/src/errors/networks/AppError';
import { APICallI, HeadersI, MethodI } from '@/src/services/api/types';
import { version } from '../../../package.json';
import useStorage from '../../hooks/storage';

const BASE_URL = Config.BASE_URL;
const useStorageHook = useStorage;
const { clearAllStorage, getStorage } = useStorageHook();

const getHeaders = async (): Promise<HeadersI> => {
  const { getStorage: getStorageFunc } = useStorageHook();
  return {
    'x-api-key': Config.API_KEY || '',
    'Content-Type': 'application/json',
    Accept: 'application/json',
    'x-device-id': (await getStorageFunc('deviceId')) || '',
    'x-platform': Platform.OS,
    'x-version-no': version,
  };
};

export const apiCall = async <PayloadT = unknown, ResponseT = unknown>(
  path: string,
  method: MethodI,
  { isAuth = true, payload, query, routeId, headers }: APICallI<PayloadT>,
): Promise<ResponseT> => {
  try {
    const baseUrl =
      Config.ENV === 'development'
        ? path.startsWith('/communication/')
          ? 'http://localhost:4001'
          : path.startsWith('/chat/')
            ? 'http://localhost:4002'
            : BASE_URL
        : BASE_URL;

    const url = generateUrl({
      baseUrl,
      path,
      query,
      routeId,
    });

    let baseHeaders = await getHeaders();

    if (isFilled(headers)) {
      baseHeaders = { ...baseHeaders, ...headers };
    }

    if (method === 'DELETE') {
      baseHeaders = omit(baseHeaders, ['Content-Type', 'Accept']) as HeadersI;
    }

    if (isAuth) {
      const isServicePath = path.startsWith('/communication/') || path.startsWith('/chat/');
      const tokenKey = isServicePath ? 'jwtToken' : 'token';
      const token = await getStorage(tokenKey);
      if (token) {
        baseHeaders.Authorization = `Bearer ${token}`;
      }
    }

    const options: RequestInit = {
      headers: baseHeaders,
      method,
    };

    if (isFilled(payload)) {
      options.body = JSON.stringify(payload);
    }

    const response = await fetch(url, options);

    const contentType = response.headers.get('content-type');
    const isJson = contentType?.includes('application/json');
    const json = isJson ? await response.json() : null;
    if (response.status >= 200 && response.status < 300) {
      return json as ResponseT;
    } else if (response.status === 401) {
      await clearAllStorage();
      store.dispatch(resetUserState());
    }

    throw new APIResError(response.status, json || { message: 'No response body' });
  } catch (error) {
    if (error instanceof APIResError) {
      throw error;
    } else if (error instanceof TypeError) {
      throw error;
    }
    throw new AppError('Unknown error', error as Error);
  }
};
