import EntitySearch from "@/src/components/EntitySearch"

const TroubleshootingFields = () => {
    return (
        <>
            <EntitySearch
                title={'IMO number (optional)'}
                placeholder={`Enter IMO number`}
                selectionKey="ship"
                
            />

             <EntitySearch
                title={'Equipment'}
                placeholder={`Enter equipment`}
                selectionKey="equipment"
                
            />

            <EntitySearch
                title={'Make'}
                placeholder={`Enter make`}
                selectionKey="make"
                
            />

            <EntitySearch
                title={'Model'}
                placeholder={`Enter model`}
                selectionKey="model"
                
            />

            <EntitySearch
                title={'Department type'}
                placeholder={`Enter department type`}
                selectionKey="department"
                
            />
        </>
    )
}

export default TroubleshootingFields