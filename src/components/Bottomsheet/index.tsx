import React from 'react';
import { View, TouchableOpacity, Dimensions, DimensionValue } from 'react-native';
import Modal from 'react-native-modal';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { BottomSheetProps } from './types';

const BottomSheet: React.FC<BottomSheetProps> = ({
  visible,
  onClose,
  onModalHide,
  children,
  height,
}) => {
  const insets = useSafeAreaInsets();
  const screenHeight = Dimensions.get('window').height;

  const getHeight = (): DimensionValue => {
    if (!height) return screenHeight * 0.5;
    if (typeof height === 'string') {
      const percentage = parseInt(height.replace('%', ''));
      return (screenHeight * percentage) / 100;
    }
    return height;
  };

  return (
    <View className="absolute inset-0 pointer-events-none">
      <Modal
        isVisible={visible}
        onBackdropPress={onClose}
        onBackButtonPress={onClose}
        onModalHide={onModalHide}
        style={{ margin: 0, justifyContent: 'flex-end' }}
        animationIn="slideInUp"
        animationOut="slideOutDown"
        backdropOpacity={0.6}
        backdropColor="black"
        animationInTiming={250}
        animationOutTiming={250}
        backdropTransitionInTiming={250}
        backdropTransitionOutTiming={1}
        useNativeDriverForBackdrop
        hideModalContentWhileAnimating={false}
        avoidKeyboard
        swipeDirection="down"
        onSwipeComplete={onClose}
      >
        <View
          className="bg-white rounded-t-2xl"
          style={{
            height: getHeight(),
            paddingBottom: insets.bottom,
          }}
        >
          <View className="items-center pt-2 pb-4">
            <TouchableOpacity
              onPress={onClose}
              className="w-10 h-1.5 bg-gray-300 rounded-full"
              activeOpacity={0.7}
            />
          </View>

          <View className="flex-1 px-4">{children}</View>
        </View>
      </Modal>
    </View>
  );
};

export default BottomSheet;
