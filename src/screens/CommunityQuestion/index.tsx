import SafeArea from '@/src/components/SafeArea'
import React from 'react'
import { View } from 'react-native'
import Create<PERSON><PERSON>munityHeader from '../CreateCommunity/components/CreateCommunityHeader'
import AskQuestionForm from './components/AskQuestionForm'
import { ScrollView } from 'react-native-gesture-handler'

const CommunityQuestionScreen = () => {
  return (
    <SafeArea>
        <ScrollView>
            <View className='flex-1 px-5 bg-white'>  
                    <CreateCommunityHeader currentPage={1} onNext={() => { }} />
                    <AskQuestionForm />
            </View>
            </ScrollView>
    </SafeArea>
  )
}

export default CommunityQuestionScreen