import { KeyboardAvoidingView, Platform } from 'react-native';
import RateLimitModal from '@/src/components/RateLimitModal';
import SafeArea from '@/src/components/SafeArea';
import { ChatHeader } from './components/ChatHeader';
import { useHeader } from './components/ChatHeader/useHeader';
import { ChatOptions } from './components/ChatOptions';
import { MessageInput } from './components/MessageInput';
import { MessageList } from './components/MessageList';
import { useMessageList } from './components/MessageList/useMessageList';
import { useAIChat } from './useAIChat';

const AIChatScreen = () => {
  const {
    messages,
    messageText,
    setMessageText,
    loading,
    isBottomSheetOpen,
    setIsBottomSheetOpen,
    scrollRef,
    handleSend,
    handleClearChat,
    showRateLimitModal,
    setShowRateLimitModal,
    streamedText,
    currentAiMessageId,
  } = useAIChat();

  const { messageGroups } = useMessageList(messages);
  const { handleBackPress } = useHeader();

  return (
    <SafeArea>
      <KeyboardAvoidingView
        className="flex-1"
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <ChatHeader onMenuPress={() => setIsBottomSheetOpen(true)} onBackPress={handleBackPress} />
        <MessageList
          messages={messages}
          loading={loading}
          scrollRef={scrollRef}
          messageGroups={messageGroups}
          streamedText={streamedText}
          currentAiMessageId={currentAiMessageId}
        />
        <MessageInput
          value={messageText}
          onChangeText={setMessageText}
          onSend={handleSend}
          loading={loading}
        />
        <ChatOptions
          visible={isBottomSheetOpen}
          onClose={() => setIsBottomSheetOpen(false)}
          onClearChat={handleClearChat}
        />
        <RateLimitModal
          onClose={() => setShowRateLimitModal(!showRateLimitModal)}
          visible={showRateLimitModal}
        />
      </KeyboardAvoidingView>
    </SafeArea>
  );
};

export default AIChatScreen;
