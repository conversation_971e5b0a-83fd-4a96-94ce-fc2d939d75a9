import { useRef, useState, useEffect } from 'react';
import {
  Keyboard,
  TextInput,
  View,
  Pressable,
  Text,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { useDispatch } from 'react-redux';
import UserAvatar from '@/src/components/UserAvatar';
import {
  addCommentOptimistic,
  addScrapbookCommentOptimistic,
  updateCommentWithRealId,
  updateScrapbookCommentWithRealId,
  removeCommentOptimistic,
  removeScrapbookCommentOptimistic,
  updateCommentRepliesCursor,
  updateScrapbookCommentRepliesCursor,
} from '@/src/redux/slices/content/contentSlice';
import { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import Send from '@/src/assets/svgs/Send';
import { addCommentAPI } from '@/src/networks/content/comment';
import { createScrapbookComment } from '@/src/networks/port/scrapbook';
import CommentPreview from '../CommentPreview';
import { CommentInputProps } from './types';

const MAX_COMMENT_LENGTH = 255;
const generateTempId = () => `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

const CommentInput = ({
  user,
  postId,
  parentCommentId,
  replyPreview,
  setReplyPreview,
  onCommentSuccess,
  type,
}: CommentInputProps) => {
  const [comment, setComment] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  const inputRef = useRef<TextInput>(null);
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', (e) => {
      if (Platform.OS === 'android') {
        setKeyboardHeight(e.endCoordinates.height);
      }
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      if (Platform.OS === 'android') {
        setKeyboardHeight(0);
      }
    });

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  useEffect(() => {
    if (replyPreview && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [replyPreview]);

  const handleClose = () => {
    setReplyPreview(null);
    setComment('');
    Keyboard.dismiss();
  };

  const handleSubmit = async () => {
    const trimmed = comment.trim();
    if (!trimmed || isSubmitting) return;

    const tempId = generateTempId();

    setComment('');
    setIsFocused(false);
    setReplyPreview(null);
    Keyboard.dismiss();
    setIsSubmitting(true);

    try {
      if (type === 'SCRAPBOOK_POST') {
        dispatch(
          addScrapbookCommentOptimistic({
            scrapbookPostId: postId,
            text: trimmed,
            parentCommentId,
            user,
            tempId,
          }),
        );

        const response = await createScrapbookComment({
          scrapBookPostId: postId,
          text: trimmed,
          parentCommentId,
        });

        dispatch(
          updateScrapbookCommentWithRealId({
            scrapbookPostId: postId,
            tempId,
            realId: response.id,
            parentCommentId,
            cursorId: response.cursorId,
          }),
        );

        if (parentCommentId && response.cursorId) {
          dispatch(
            updateScrapbookCommentRepliesCursor({
              scrapbookPostId: postId,
              parentCommentId,
              cursorId: response.cursorId,
            }),
          );
        }
      } else {
        dispatch(
          addCommentOptimistic({
            postId,
            text: trimmed,
            parentCommentId,
            user,
            tempId,
          }),
        );

        const response = await addCommentAPI({
          postId,
          text: trimmed,
          ...(parentCommentId && { parentCommentId }),
        });

        dispatch(
          updateCommentWithRealId({
            postId,
            tempId,
            realId: response.id,
            parentCommentId,
            cursorId: response.cursorId,
          }),
        );

        if (parentCommentId && response.cursorId) {
          dispatch(
            updateCommentRepliesCursor({
              postId,
              parentCommentId,
              cursorId: response.cursorId,
            }),
          );
        }
      }

      onCommentSuccess();
    } catch (error) {
      const removeAction =
        type === 'SCRAPBOOK_POST'
          ? removeScrapbookCommentOptimistic({
              scrapbookPostId: postId,
              commentId: tempId,
              parentCommentId,
            })
          : removeCommentOptimistic({
              postId,
              commentId: tempId,
              parentCommentId,
            });

      dispatch(removeAction);

      showToast({
        type: 'error',
        message: 'Failed to add comment',
        description: 'Please try again later',
      });
    } finally {
      setIsSubmitting(false);
    }
  };



  const handleCommentText = (text: string) => {
    if (text.length <= MAX_COMMENT_LENGTH) {
      setComment(text);
    }
  };

  const isReply = Boolean(parentCommentId && replyPreview);
  const canSubmit = comment.trim().length > 0 && !isSubmitting;
  const isNearLimit = comment.length > MAX_COMMENT_LENGTH * 0.8;


  const isAndroid15Plus = Platform.OS === 'android' && Number(Platform.Version) >= 35;
  const paddingBottom = isAndroid15Plus && keyboardHeight > 0
    ? Math.max(keyboardHeight - 50, 0)
    : 0;
  const marginBottom = isAndroid15Plus && keyboardHeight > 0 ? 40 : 0;

  return (
    <View
      className="bg-white border-t border-gray-200"
      style={{
        paddingBottom: paddingBottom,
        marginBottom: marginBottom,
      }}
    >
      {replyPreview && <CommentPreview comment={replyPreview} handleClose={handleClose} />}

      <View className="p-4">
        <View className="flex-row items-start gap-3">
          <UserAvatar avatarUri={user.avatar} height={40} width={40} />

          <View className="flex-1">
            <View className="relative">
              <TextInput
                ref={inputRef}
                className="bg-gray-100 rounded-2xl text-black px-4 py-3 pr-12 text-base min-h-[44px] max-h-[120px]"
                placeholder={
                  isReply ? `Reply to ${replyPreview?.Profile?.name}...` : 'Write a comment...'
                }
                placeholderTextColor="#9CA3AF"
                value={comment}
                onChangeText={handleCommentText}
                maxLength={MAX_COMMENT_LENGTH}
                multiline
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
                autoCorrect
                editable={!isSubmitting}
                textAlignVertical="top"
              />
              <Pressable
                onPress={handleSubmit}
                disabled={!canSubmit}
                className={`absolute right-2 bottom-2 w-8 h-8 rounded-full items-center justify-center ${
                  canSubmit ? 'bg-green-800' : 'bg-gray-300'
                }`}
              >
                {isSubmitting ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <Send width={1.6} height={1.6} color="white" strokeWidth={2} />
                )}
              </Pressable>
            </View>

            {(isFocused || isNearLimit) && (
              <View className="flex-row justify-between items-center mt-2 px-2">
                <Text className={`text-xs ${isNearLimit ? 'text-orange-500' : 'text-gray-500'}`}>
                  {comment.length}/{MAX_COMMENT_LENGTH}
                </Text>
                {isReply && (
                  <Pressable onPress={handleClose}>
                    <Text className="text-xs text-green-800 font-medium">Cancel Reply</Text>
                  </Pressable>
                )}
              </View>
            )}
          </View>
        </View>
      </View>
    </View>
  );
};

export default CommentInput;
