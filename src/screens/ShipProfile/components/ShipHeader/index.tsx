import type React from 'react';
import { Image, Pressable, StatusBar, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import ChevronLeft from '@/src/assets/svgs/ChevronLeft';
import type { ShipHeaderProps } from './types';

const ShipHeader: React.FC<ShipHeaderProps> = ({ imageUri, label }) => {
  const navigation = useNavigation<BottomTabNavigationI>();

  const handleBack = () => {
    navigation.goBack();
  };

  const handleViewMore = () => {};

  return (
    <View className="relative h-96">
      <View className="absolute top-0 left-0 w-full h-full">
        {imageUri ? (
          <Image source={{ uri: imageUri }} className="w-full h-full" resizeMode="cover" />
        ) : (
          <View className="w-full h-full bg-gray-300" />
        )}
        <LinearGradient
          colors={['rgba(0, 0, 0, 0.7)', 'rgba(0, 0, 0, 0)']}
          locations={[0, 1]}
          className="absolute top-0 left-0 w-full h-1/3"
        />
      </View>
      <StatusBar barStyle="light-content" />
      <View className="flex-row justify-between items-center mt-10 px-4">
        <Pressable onPress={handleBack} className="rounded-full bg-black opacity-50">
          <ChevronLeft stroke="#ffffff" />
        </Pressable>
        {/* <Pressable onPress={handleViewMore} className="px-2 rounded-md bg-black opacity-50 p-1">
          <Text className="text-white text-sm underline">View all</Text>
        </Pressable> */}
      </View>
      <View className="absolute bottom-4 left-0 right-0 flex-row justify-between items-center px-4">
        <Text className="text-white text-xl leading-6 font-semibold max-w-[70%]">
          {label || '-'}
        </Text>
      </View>
    </View>
  );
};

export default ShipHeader;
