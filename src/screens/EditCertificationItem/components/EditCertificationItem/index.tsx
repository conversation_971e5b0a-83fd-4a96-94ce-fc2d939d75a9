import { useEffect } from 'react';
import React from 'react';
import { ActivityIndicator, Pressable, ScrollView, Text, View } from 'react-native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import ChipInput from '@/src/components/ChipInput';
import DatePicker from '@/src/components/DatePicker';
import EntitySearch from '@/src/components/EntitySearch';
import PdfDownload from '@/src/components/PdfDownload';
import Select from '@/src/components/Select';
import TextInput from '@/src/components/TextInput';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import TrashBin from '@/src/assets/svgs/TrashBin';
import Upload from '@/src/assets/svgs/Upload';
import { EditCertificationItemPropsI } from './types';
import { useEditCertificationItem } from './useHook';

export const EditCertificationItem = ({
  onBack,
  profileId,
  certificationId,
}: EditCertificationItemPropsI) => {
  const certificateCourseSelection = useSelector(selectSelectionByKey('certificate-course'));
  const providerSelection = useSelector(selectSelectionByKey('entity'));

  const {
    methods,
    typeOptions,
    courseOptions,
    isSubmitting,
    onSubmit,
    selectedFile,
    handleAttachment,
    localSkills,
    setLocalSkills,
    loading,
    handleDownload,
    handleRemoveFile,
    isFileRemoved,
    clearFields,
  } = useEditCertificationItem(profileId, certificationId);

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
  } = methods;

  useEffect(() => {
    if (certificateCourseSelection) {
      methods.setValue('course', certificateCourseSelection);
    }
    if (providerSelection) {
      methods.setValue('provider', providerSelection);
    }

    return () => {
      clearFields();
    };
  }, [certificateCourseSelection, providerSelection, methods]);

  const validFromDate = watch('validFrom');
  const validUntilDate = watch('validUntil');

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-white" showsVerticalScrollIndicator={false}>
      <View className="px-4">
        <View className="flex-row items-center justify-between py-4">
          <BackButton onBack={onBack} label="Edit certification" />
          <Pressable onPress={handleSubmit(onSubmit)} disabled={isSubmitting}>
            <Text className="text-lg font-medium text-[#448600]">
              {isSubmitting ? 'Saving...' : 'Save'}
            </Text>
          </Pressable>
        </View>

        <View className="mb-3">
          <Text className="mb-2 text-base font-medium">Certificate type</Text>
          <Controller
            control={control}
            name="type"
            rules={{ required: 'Certificate type is required' }}
            render={({ field: { onChange, value } }) => (
              <Select
                options={typeOptions}
                value={value}
                onChange={onChange}
                placeholder="Select certificate type"
                error={errors.type?.message}
              />
            )}
          />
        </View>

        <Controller
          control={control}
          name="course"
          rules={{ required: 'Course is required' }}
          render={() => (
            <EntitySearch
              placeholder="Search course"
              selectionKey={`certificate-course/${typeOptions.find((type) => type.title === methods.watch('type'))?.id}`}
              title="Certificate Course"
              data={
                certificateCourseSelection
                  ? certificateCourseSelection.name
                  : methods.watch('course').name
              }
            />
          )}
        />

        <Controller
          control={control}
          name="provider"
          rules={{ required: 'Provider is required' }}
          render={() => (
            <EntitySearch
              placeholder="Search provider"
              selectionKey="entity"
              title="Provider"
              data={providerSelection ? providerSelection.name : methods.watch('provider').name}
            />
          )}
        />

        <View className="mb-6 mt-3">
          <Controller
            control={control}
            name="providerWebsite"
            render={({ field: { onChange, value } }) => (
              <TextInput
                label="Provider website (optional)"
                placeholder="Enter provider website"
                value={value}
                onChangeText={onChange}
                error={errors.providerWebsite?.message}
              />
            )}
          />
        </View>

        <View className="flex-row mb-6">
          <View className="flex-1 mr-2">
            <Controller
              control={control}
              name="validFrom"
              rules={{ required: 'Start date is required' }}
              render={({ field: { onChange } }) => (
                <DatePicker
                  title="Valid from"
                  selectedDate={validFromDate}
                  onDateChange={(date) => {
                    if (date instanceof Date) {
                      onChange(date.toISOString().split('T')[0]);
                    }
                  }}
                  showMonthYear={true}
                />
              )}
            />
          </View>
          <View className="flex-1 ml-2">
            <Controller
              control={control}
              name="validUntil"
              rules={{ required: 'End date is required' }}
              render={({ field: { onChange } }) => (
                <DatePicker
                  title="Valid until"
                  selectedDate={validUntilDate}
                  onDateChange={(date) => {
                    if (date instanceof Date) {
                      onChange(date.toISOString().split('T')[0]);
                    }
                  }}
                  showMonthYear={true}
                />
              )}
            />
          </View>
        </View>

        <View className="mb-6">
          <Text className="mb-2 text-base font-medium">Certificate document (optional)</Text>
          {selectedFile || (methods.watch('documentUrl') && !isFileRemoved) ? (
            <View className="border border-dashed border-[#E5E5E5] rounded-lg p-6">
              <View className="flex-row items-center justify-between">
                <View className="flex-1">
                  {selectedFile ? (
                    <Text className="text-sm font-medium">{selectedFile}</Text>
                  ) : (
                    <PdfDownload onDownload={handleDownload} />
                  )}
                </View>
                <View className="flex-row ml-3">
                  <Pressable onPress={handleAttachment} className="mr-3 p-2">
                    <Upload width={2} height={2} />
                  </Pressable>
                  <Pressable onPress={handleRemoveFile} className="p-2">
                    <TrashBin width={2} height={2} color="#DC2626" />
                  </Pressable>
                </View>
              </View>
            </View>
          ) : isFileRemoved ? (
            <View className="border border-dashed border-[#E5E5E5] rounded-lg p-6">
              <View className="flex-row items-center justify-between">
                <Text className="text-sm text-red-600 flex-1">File marked for removal</Text>
                <Pressable onPress={handleAttachment} className="p-2">
                  <Upload width={2} height={2} />
                </Pressable>
              </View>
            </View>
          ) : (
            <Pressable
              className="border border-dashed border-[#E5E5E5] rounded-lg p-6 items-center"
              onPress={handleAttachment}
            >
              <Upload />
              <Text className="mt-2 font-medium">Upload file</Text>
              <Text style={{ fontSize: 12, color: '#6B7280' }}>
                Supported file types:
                <Text style={{ fontWeight: '500', color: '#4B5563' }}> .pdf </Text>
                (Max 5 MB),
                <Text style={{ fontWeight: '500', color: '#4B5563' }}> .jpg </Text>
                and
                <Text style={{ fontWeight: '500', color: '#4B5563' }}> .jpeg </Text>
                (Max 2 MB).
              </Text>
            </Pressable>
          )}
        </View>

        <View className="mb-6">
          <ChipInput
            title="Skills"
            placeholder="Add a skill"
            chips={localSkills}
            onRemove={(id) => setLocalSkills((prev) => prev.filter((s) => s.id !== id))}
          />
        </View>
      </View>
    </ScrollView>
  );
};

export default EditCertificationItem;
