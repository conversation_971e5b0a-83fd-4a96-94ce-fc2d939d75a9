import React from 'react';
import {
  View,
  Text,
  Pressable,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Image,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { Controller } from 'react-hook-form';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import TextInput from '@/src/components/TextInput';
import Button from '@/src/components/Button';
import Close from '@/src/assets/svgs/Close';
import Attachment from '@/src/assets/svgs/Attachment';
import TrashBin from '@/src/assets/svgs/TrashBin';
import type { CreateQuestionFormProps } from './types';
import useCreateQuestionForm from './useHook';

const CreateQuestionForm = ({
  onSuccess,
  editing,
  questionId,
}: CreateQuestionFormProps) => {
  const navigation = useNavigation();
  const {
    control,
    errors,
    isSubmitting,
    isLoading,
    attachments,
    titleInputRef,
    descriptionInputRef,
    handleSubmit,
    handleAttachments,
    handleDeleteAttachment,
    focusTitleInput,
    isFormValid,
    watchedValues,
  } = useCreateQuestionForm({ onSuccess, editing, questionId });

  const handleClose = () => {
    navigation.goBack();
  };

  useFocusEffect(() => {
    const timer = setTimeout(
      () => {
        focusTitleInput();
      },
      Platform.OS === 'android' ? 400 : 100,
    );

    return () => clearTimeout(timer);
  });

  const renderAttachmentThumbnail = (attachment: any, index: number) => {
    const isImage = attachment.type.startsWith('image/');
    const isPdf = attachment.type === 'application/pdf';
    const isText = attachment.type === 'text/plain';

    return (
      <View key={`attachment-${index}`} style={styles.attachmentItem}>
        <View style={styles.attachmentIcon}>
          {isImage ? (
            <Image
              source={{ uri: attachment.uri }}
              style={styles.attachmentImage}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.fileIcon}>
              <Text style={styles.fileIconText}>
                {isPdf ? 'PDF' : isText ? 'TXT' : 'FILE'}
              </Text>
            </View>
          )}
        </View>
        <View style={styles.attachmentInfo}>
          <Text style={styles.attachmentName} numberOfLines={1}>
            {attachment.filename}
          </Text>
          <Text style={styles.attachmentType}>
            {attachment.type}
          </Text>
        </View>
        <Pressable
          onPress={() => handleDeleteAttachment(index)}
          style={styles.deleteButton}
          hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
        >
          <TrashBin color="#EF4444" width={16} height={16} />
        </Pressable>
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#22C55E" />
        <Text style={styles.loadingText}>Loading question...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      style={styles.container}
    >
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Pressable
              onPress={handleClose}
              style={styles.closeButton}
              hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
            >
              <Close height={2.5} width={2.5} />
            </Pressable>
            <Text style={styles.headerTitle}>
              2/2
            </Text>
          </View>
          <View style={styles.headerRight}>
            <Pressable onPress={handleAttachments} style={styles.attachButton}>
              <Attachment />
            </Pressable>
            <Button
              className="w-auto px-4 rounded-full"
              label="Send request"
              onPress={handleSubmit}
              loading={isSubmitting}
              disabled={isSubmitting || !isFormValid}
            />
          </View>
        </View>

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          {/* Title Field */}
          <View style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>
              Title
            </Text>
            <Controller
              control={control}
              name="title"
              render={({ field: { onChange, value } }) => (
                <TextInput
                  inputRef={titleInputRef}
                  placeholder="Enter question title"
                  value={value}
                  onChangeText={onChange}
                  error={errors.title?.message}
                  className="mb-0"
                  maxLength={200}
                  returnKeyType="next"
                  onSubmitEditing={() => descriptionInputRef.current?.focus()}
                />
              )}
            />
          </View>

          {/* Description Field */}
          <View style={styles.fieldContainer}>
            <Text style={styles.fieldLabel}>
              Description
            </Text>
            <Controller
              control={control}
              name="description"
              render={({ field: { onChange, value } }) => (
                <TextInput
                  inputRef={descriptionInputRef}
                  type="textarea"
                  placeholder="Enter question description"
                  value={value}
                  onChangeText={onChange}
                  error={errors.description?.message}
                  className="mb-0"
                  maxLength={2000}
                  numberOfLines={8}
                />
              )}
            />
          </View>

          {/* Attachments */}
          {attachments.length > 0 && (
            <View style={styles.attachmentsContainer}>
              <Text style={styles.attachmentsTitle}>
                Attachments ({attachments.length}/5)
              </Text>
              {attachments.map((attachment, index) =>
                renderAttachmentThumbnail(attachment, index),
              )}
            </View>
          )}

          {/* Character Count */}
          <View style={styles.characterCount}>
            <Text style={styles.characterCountText}>
              Title: {watchedValues.title?.length || 0}/200
            </Text>
            <Text style={styles.characterCountText}>
              Description: {watchedValues.description?.length || 0}/2000
            </Text>
          </View>
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  loadingText: {
    marginTop: 8,
    color: '#6B7280',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  closeButton: {
    borderRadius: 20,
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
    color: '#111827',
  },
  attachButton: {
    padding: 8,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
  },
  fieldContainer: {
    marginBottom: 24,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 8,
  },
  attachmentsContainer: {
    marginBottom: 24,
  },
  attachmentsTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 12,
  },
  attachmentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    marginBottom: 8,
  },
  attachmentIcon: {
    marginRight: 12,
  },
  attachmentImage: {
    width: 40,
    height: 40,
    borderRadius: 4,
  },
  fileIcon: {
    width: 40,
    height: 40,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fileIconText: {
    fontSize: 10,
    fontWeight: '500',
    color: '#6B7280',
  },
  attachmentInfo: {
    flex: 1,
  },
  attachmentName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#111827',
  },
  attachmentType: {
    fontSize: 12,
    color: '#6B7280',
  },
  deleteButton: {
    padding: 8,
  },
  characterCount: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 'auto',
    paddingTop: 16,
  },
  characterCountText: {
    fontSize: 12,
    color: '#6B7280',
  },
});

export default CreateQuestionForm;
