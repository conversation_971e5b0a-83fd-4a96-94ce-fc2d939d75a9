import type React from 'react';
import { useState, useCallback } from 'react';
import { TextInput, View, Text, Pressable } from 'react-native';
import { useDispatch } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Tabs from '@/src/components/Tabs';
import { resetSearchPosts } from '@/src/redux/slices/content/contentSlice';
import { addSearch } from '@/src/redux/slices/globalsearch/globalSearchSlice';
import type { AppDispatch } from '@/src/redux/store';
import { debounceAsync } from '@/src/utilities/search/debounce';
import { fetchGlobalSearchResults } from '@/src/utilities/search/globalSearch';
import type { GlobalSearchCategory, GlobalSearchResponse } from '@/src/utilities/search/types';
import RecentSearches from '../RecentSearches';
import SearchResults from '../SearchResults';
import type { SearchCategoryTabs } from '../SearchResults/types';
import type { SearchBoxPropsI } from './types';

const tabs: SearchCategoryTabs[] = [
  { id: 'people', label: 'People' },
  { id: 'post', label: 'Posts' },
  { id: 'ship', label: 'Ship' },
  { id: 'port', label: 'Port' },
  { id: 'organization', label: 'Organization' },
  { id: 'institution', label: 'Institution' },
];

const Searchbox: React.FC<SearchBoxPropsI> = ({ onBack, onError }) => {
  const [searchData, setSearchData] = useState<string>('');
  const [showRecent, setShowRecent] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<GlobalSearchCategory>('people');
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [searchResults, setSearchResults] = useState<
    Record<GlobalSearchCategory, GlobalSearchResponse>
  >({
    people: { data: [], total: 0 },
    post: { data: [], total: 0 },
    ship: { data: [], total: 0 },
    port: { data: [], total: 0 },
    organization: { data: [], total: 0 },
    institution: { data: [], total: 0 },
  });
  const [currentPage, setCurrentPage] = useState<Record<GlobalSearchCategory, number>>({
    people: 0,
    post: 0,
    ship: 0,
    port: 0,
    organization: 0,
    institution: 0,
  });
  const [lastSearchQuery, setLastSearchQuery] = useState<string>('');

  const dispatch = useDispatch<AppDispatch>();

  const performSearch = async (query: string, category: GlobalSearchCategory, page = 0) => {
    if (!query.trim()) {
      setSearchResults((prev) => ({
        ...prev,
        [category]: { data: [], total: 0 },
      }));
      return;
    }

    try {
      const results = await fetchGlobalSearchResults(category, query, page);
      if (results) {
        setSearchResults((prev) => ({
          ...prev,
          [category]:
            page === 0
              ? results
              : { data: [...prev[category].data, ...results.data], total: results.total },
        }));
      }
    } catch (error) {
      const errorMessage = `Failed to search ${category}: ${error instanceof Error ? error.message : 'Unknown error'}`;
      if (onError) {
        onError(new Error(errorMessage));
      }
      setSearchResults((prev) => ({
        ...prev,
        [category]: { data: [], total: 0 },
      }));
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const debouncedSearch = debounceAsync(
    performSearch as (...args: unknown[]) => Promise<unknown>,
    500,
  );

  const handleSubmit = async () => {
    const trimmed = searchData.trim();
    if (trimmed) {
      setLoading(true);
      setCurrentPage((prev) => ({ ...prev, [activeTab]: 0 }));
      setLastSearchQuery(trimmed);
      dispatch(addSearch({ searchText: trimmed, category: 'people' }));
      setShowRecent(false);
      debouncedSearch(trimmed, activeTab, 0);
    }
  };

  const handleTabChange = useCallback(
    (tab: GlobalSearchCategory) => {
      setActiveTab(tab);
      setCurrentPage((prev) => ({ ...prev, [tab]: 0 }));
      dispatch(resetSearchPosts());

      if (lastSearchQuery.trim()) {
        if (!searchResults[tab].data.length) {
          setLoading(true);
          performSearch(lastSearchQuery, tab, 0);
        }
      }
    },
    [lastSearchQuery, searchResults, dispatch],
  );

  const handleSearchChange = (text: string) => {
    setSearchData(text);
    if (text.trim() === '') {
      setSearchResults({
        people: { data: [], total: 0 },
        post: { data: [], total: 0 },
        ship: { data: [], total: 0 },
        port: { data: [], total: 0 },
        organization: { data: [], total: 0 },
        institution: { data: [], total: 0 },
      });
      setShowRecent(true);
      setLastSearchQuery('');
    }
  };

  const handleLoadMore = () => {
    if (
      loading ||
      !lastSearchQuery.trim() ||
      searchResults[activeTab].data.length === searchResults[activeTab].total
    )
      return;
    const nextPage = currentPage[activeTab] + 1;
    setCurrentPage((prev) => ({ ...prev, [activeTab]: nextPage }));
    setLoading(true);
    debouncedSearch(lastSearchQuery, activeTab, nextPage);
  };

  const handleRefresh = async () => {
    if (!lastSearchQuery.trim()) return;
    setRefreshing(true);
    setCurrentPage((prev) => ({ ...prev, [activeTab]: 0 }));
    await performSearch(lastSearchQuery, activeTab, 0);
  };

  return (
    <View className="flex-1 bg-white">
      <View className="px-4">
        <View className="flex-row items-center space-x-3">
          <BackButton onBack={onBack} label="" />
          <View className="flex-1 flex-row items-center bg-gray-100 rounded-xl px-3">
            <TextInput
              autoFocus
              placeholder="Search..."
              placeholderTextColor="#6b7280"
              value={searchData}
              onChangeText={handleSearchChange}
              onSubmitEditing={handleSubmit}
              returnKeyType="search"
              className="flex-1 text-black py-3"
            />

            {searchData.length > 0 && (
              <Pressable
                onPress={() => {
                  setSearchData('');
                  setSearchResults({
                    people: { data: [], total: 0 },
                    post: { data: [], total: 0 },
                    ship: { data: [], total: 0 },
                    port: { data: [], total: 0 },
                    organization: { data: [], total: 0 },
                    institution: { data: [], total: 0 },
                  });
                  setShowRecent(true);
                  setLastSearchQuery('');
                }}
                className="p-1"
              >
                <Text className="text-gray-500">✕</Text>
              </Pressable>
            )}
          </View>
        </View>
      </View>
      <View className="flex-1">
        {showRecent ? (
          <RecentSearches
            setSearchData={setSearchData}
            setActiveTab={setActiveTab}
            setLoading={setLoading}
            setShowRecent={setShowRecent}
            debouncedSearch={debouncedSearch}
            setLastSearchQuery={setLastSearchQuery}
          />
        ) : (
          <>
            <Tabs
              tabs={tabs}
              activeTab={activeTab}
              onTabChange={(tab) => {
                handleTabChange(tab as GlobalSearchCategory);
              }}
              disabled={loading}
            />
            <SearchResults
              activeTab={activeTab}
              searchResults={searchResults[activeTab]}
              loading={loading}
              onLoadMore={handleLoadMore}
              searchText={lastSearchQuery}
              refreshing={refreshing}
              onRefresh={handleRefresh}
              onError={onError}
            />
          </>
        )}
      </View>
    </View>
  );
};

export default Searchbox;
