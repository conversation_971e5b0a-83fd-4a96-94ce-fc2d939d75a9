import { useEffect, useState, useRef } from 'react';
import {
  Pressable,
  Text,
  View,
  Image,
  ActivityIndicator,
  ScrollView,
  InteractionManager,
  Platform,
  Keyboard,
  KeyboardAvoidingView,
  TextInput,
} from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import Config from 'react-native-config';
import EventSource from 'react-native-sse';
import BottomSheet from '@/src/components/Bottomsheet';
import Button from '@/src/components/Button';
import RateLimitModal from '@/src/components/RateLimitModal';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import Attachment from '@/src/assets/svgs/Attachment';
import Close from '@/src/assets/svgs/Close';
import useStorage from '@/src/hooks/storage';
import CustomPromptModal from '../CustomPrompt';
import EnhanceTextModal from '../EnhanceTextModal';
import PostThumbnail from '../PostThumbnail';
import { getRandomPrompts } from '../PromptsData';
import type { CreatePostFormProps } from './types';
import useCreatePostForm from './useHook';

const AI_URL = Config.AI_URL;

const CreatePostForm = ({
  onSuccess,
  type,
  portUnLocode,
  editing,
  postId,
}: CreatePostFormProps) => {
  const {
    caption,
    setCaption,
    isSubmitting,
    isLoading,
    textInputRef,
    handlePost,
    handleAttachments,
    isCaptioning,
    media,
    handleDeletePost,
    handleDone,
    handlePostThumbnail,
    handlePreview,
    isDone,
    selectedImage,
    postText,
    setPostText,
    setMedia,
    setIsCaptioning,
  } = useCreatePostForm({ onSuccess, type, portUnLocode, editing, postId });

  const [showCustomPrompt, setShowCustomPrompt] = useState(false);
  const [showEnhanceModal, setShowEnhanceModal] = useState(false);
  const [showAIBottomSheet, setShowAIBottomSheet] = useState(false);
  const [randomPrompts, setRandomPrompts] = useState<string[]>([]);
  const [aiMode, setAiMode] = useState<'global' | 'image'>('global');
  const [isGenerating, setIsGenerating] = useState(false);
  const [aiText, setAiText] = useState('');
  const [streamedText, setStreamedText] = useState('');
  const [showRateLimitModal, setShowRateLimitModal] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  const { getStorage } = useStorage();
  const eventSourceRef = useRef<EventSource | null>(null);
  const streamingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const scrollViewRef = useRef<ScrollView>(null);
  const navigation = useNavigation<BottomTabNavigationI>();

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', (e) => {
      setKeyboardHeight(e.endCoordinates.height);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardHeight(0);
    });

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  useEffect(() => {
    if (!aiText || isGenerating) {
      return;
    }

    let currentIndex = 0;
    setStreamedText('');

    const streamText = () => {
      if (currentIndex < aiText.length) {
        currentIndex = Math.min(currentIndex + 4, aiText.length);
        setStreamedText(aiText.slice(0, currentIndex));

        if (currentIndex < aiText.length) {
          streamingTimeoutRef.current = setTimeout(streamText, 50);
        } else {
          if (aiMode === 'global') {
            const maxLength = type === 'USER_POST' ? 2000 : 1000;
            const splicedText = aiText.slice(0, maxLength);
            setCaption(splicedText);
          } else {
            const splicedText = aiText.slice(0, 255);
            setPostText(splicedText);
          }
          setStreamedText('');
          setAiText('');
        }
      }
    };

    streamText();

    return () => {
      if (streamingTimeoutRef.current) {
        clearTimeout(streamingTimeoutRef.current);
      }
    };
  }, [aiText, isGenerating, aiMode, type, setCaption, setPostText]);

  const generateContent = async (prompt: string, mode: 'global' | 'image') => {
    setIsGenerating(true);
    setAiText('');
    setStreamedText('');

    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    if (streamingTimeoutRef.current) {
      clearTimeout(streamingTimeoutRef.current);
      streamingTimeoutRef.current = null;
    }

    const maxChars = mode === 'image' ? 150 : type === 'USER_POST' ? 1500 : 800;
    const promptType = mode === 'image' ? 'image caption' : 'social media post';

    const url = `${AI_URL}/query/general?query=${encodeURIComponent(
      `Generate a concise ${promptType} of max ${maxChars} characters or less, not more than that. Include relevant hashtags if appropriate. The content should be maritime-focused and professional, Just return the post text content.\n\nPrompt: ${prompt}`,
    )}&device=${await getStorage('deviceToken')}`;

    const es = new EventSource(url);
    eventSourceRef.current = es;

    let accumulatedText = '';

    es.addEventListener('open', () => {
      console.log('✅ EventSource connection opened successfully');
    });

    es.addEventListener('message', (event) => {
      try {
        const parsed = JSON.parse(event.data ?? '');
        const text = parsed.text;

        if (text === '[DONE]') {
          es.close();
          eventSourceRef.current = null;
          setIsGenerating(false);
          if (accumulatedText.trim()) {
            setAiText(accumulatedText);
          }
          return;
        }

        accumulatedText += text;
      } catch (err) {
        console.error('❌ Failed to parse SSE JSON:', event.data);
      }
    });

    es.addEventListener('error', (event) => {
      es.close();
      eventSourceRef.current = null;
      setIsGenerating(false);
      setAiText('');
      setStreamedText('');
      setShowRateLimitModal(true);
    });

    try {
      const response = await fetch(url, { method: 'HEAD' });
      if (response.status === 429) {
        es.close();
        eventSourceRef.current = null;
        setIsGenerating(false);
        setShowRateLimitModal(true);
        return;
      }
    } catch (error: unknown) {
      const fetchError = error as { status?: number };
      if (fetchError && fetchError.status === 429) {
        es.close();
        eventSourceRef.current = null;
        setIsGenerating(false);
        setShowRateLimitModal(true);
        return;
      }
    }
  };

  const stopGeneration = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    if (streamingTimeoutRef.current) {
      clearTimeout(streamingTimeoutRef.current);
      streamingTimeoutRef.current = null;
    }

    setIsGenerating(false);
    setAiText('');
    setStreamedText('');
  };

  const focusTextInput = () => {
    if (textInputRef.current && !isLoading) {
      if (Platform.OS === 'android') {
        InteractionManager.runAfterInteractions(() => {
          setTimeout(() => {
            textInputRef.current?.focus();
          }, 200);
        });
      } else {
        requestAnimationFrame(() => {
          textInputRef.current?.focus();
        });
      }
    }
  };

  const resetForm = () => {
    if (textInputRef.current) {
      textInputRef.current.clear();
    }
    setCaption('');
    setPostText('');
    setMedia([]);
    setIsCaptioning(false);
  };

  const handleClose = () => {
    resetForm();
    navigation.goBack();
  };

  const handleImageCaptionChange = (text: string) => {
    if (text.length <= 255) {
      setPostText(text);
    }
  };

  const handleGlobalCaptionChange = (text: string) => {
    if (text.length <= (type === 'USER_POST' ? 2000 : 1000)) {
      setCaption(text);
    }
  };

  const handleShowAIOptions = (mode: 'global' | 'image') => {
    setAiMode(mode);
    setRandomPrompts(getRandomPrompts(6));
    setShowAIBottomSheet(true);
  };

  const handleRefreshPrompts = () => {
    setRandomPrompts(getRandomPrompts(6));
  };

  const handlePromptSelect = (prompt: string) => {
    generateContent(prompt, aiMode);
    setShowAIBottomSheet(false);
  };

  const handleCustomPrompt = (prompt: string) => {
    generateContent(prompt, aiMode);
    setShowAIBottomSheet(false);
  };

  const handleEnhanceText = (prompt: string) => {
    generateContent(prompt, aiMode);
  };

  const handleShowEnhanceModal = () => {
    setAiMode(isCaptioning ? 'image' : 'global');
    setShowEnhanceModal(true);
  };

  const handleMediaPreview = (post: any) => {
    if (handlePreview && typeof handlePreview === 'function') {
      handlePreview(post);
      setIsCaptioning(true);
    }
  };

  const handleThumbnailPress = (uri: string) => {
    if (handlePostThumbnail && typeof handlePostThumbnail === 'function') {
      handlePostThumbnail(uri);
    }
  };

  useFocusEffect(() => {
    const timer = setTimeout(
      () => {
        focusTextInput();
      },
      Platform.OS === 'android' ? 400 : 100,
    );

    return () => clearTimeout(timer);
  });

  useEffect(() => {
    const unsubscribe = navigation.addListener('blur', () => {
      resetForm();
    });

    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
      if (streamingTimeoutRef.current) {
        clearTimeout(streamingTimeoutRef.current);
      }
    };
  }, []);

  if (isLoading) {
    return (
      <View className="flex-1 bg-white justify-center items-center">
        <ActivityIndicator size="small" color="#448600" />
        <Text className="mt-2 text-gray-600">Loading post...</Text>
      </View>
    );
  }

  const maxLength = type === 'USER_POST' ? 2000 : 1000;
  const hasGlobalText = caption.trim().length > 0;
  const hasImageText = postText.trim().length > 0;
  const currentText = isCaptioning ? postText : caption;
  const isStreaming = streamedText.length > 0;
  const hasMedia = media.length > 0 && isDone && !isCaptioning;
  const bottomButtonHeight = 60;

  // Only apply keyboard height adjustment for Android version 15 (API level 35) or higher
  const shouldApplyKeyboardAdjustment = Platform.OS === 'android' && Platform.Version >= 35;
  const adjustedKeyboardHeight = shouldApplyKeyboardAdjustment ? keyboardHeight : 0;

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      className="flex-1"
    >
      <View className="flex-1 bg-white">
        <View className="flex-row justify-between items-center px-4 py-2 border-b border-gray-100">
          <View className="flex-row items-center">
            <Pressable
              onPress={handleClose}
              className="rounded-full active:bg-gray-100 p-1"
              hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
            >
              <Close height={2.5} width={2.5} />
            </Pressable>
            <Text className="text-lg font-semibold ml-2 text-gray-900">
              {type === 'USER_POST'
                ? editing
                  ? 'Edit Post'
                  : 'Create Post'
                : editing
                  ? 'Edit Scrapbook Note'
                  : 'Create Scrapbook Note'}
            </Text>
          </View>
          <View className="flex-row gap-2 items-center">
            {type === 'USER_POST' && (
              <Pressable className="opacity-50" onPress={handleAttachments}>
                <Attachment />
              </Pressable>
            )}
            {isCaptioning ? (
              <Pressable onPress={handleDone}>
                <Text className="text-primaryGreen font-medium">Done</Text>
              </Pressable>
            ) : (
              <Button
                className="w-auto px-4 rounded-full"
                label={editing ? 'Update' : 'Post'}
                onPress={handlePost}
                loading={isSubmitting}
                disabled={isSubmitting || !caption.trim()}
              />
            )}
          </View>
        </View>

        <View
          className="flex-1"
          style={{ paddingBottom: bottomButtonHeight + adjustedKeyboardHeight }}
        >
          {!isCaptioning && (
            <View className="flex-1 px-4 pt-2">
              <View className="relative flex-1">
                <TextInput
                  ref={textInputRef}
                  value={
                    (isGenerating || isStreaming) && aiMode === 'global' ? streamedText : caption
                  }
                  onChangeText={handleGlobalCaptionChange}
                  placeholder={
                    isGenerating
                      ? 'AI is thinking...'
                      : isStreaming
                        ? 'AI is writing...'
                        : "What's on your mind?"
                  }
                  placeholderTextColor="#9ca3af"
                  multiline={true}
                  textAlignVertical="top"
                  autoFocus
                  maxLength={maxLength}
                  editable={!isGenerating && !isStreaming}
                  blurOnSubmit={false}
                  returnKeyType={Platform.OS === 'ios' ? 'default' : 'none'}
                  scrollEnabled={true}
                  className="flex-1 text-base text-black leading-[22px] p-3 pb-9 text-top min-h-[200px]"
                />

                {(isGenerating || isStreaming) && aiMode === 'global' && (
                  <View className="absolute top-2 right-2">
                    <Pressable
                      onPress={stopGeneration}
                      className="bg-red-500 px-2 py-1 rounded-full"
                      hitSlop={{ top: 4, bottom: 4, left: 4, right: 4 }}
                    >
                      <Text className="text-white text-xs font-medium">Stop</Text>
                    </Pressable>
                  </View>
                )}

                <View className="absolute bottom-2 right-3">
                  <View className="bg-gray-100 px-2 py-1 rounded-full">
                    <Text
                      className={`text-xs ${caption.length > maxLength * 0.9 ? 'text-orange-500' : 'text-gray-500'}`}
                    >
                      {caption.length}/{maxLength}
                    </Text>
                  </View>
                </View>
              </View>

              {hasMedia && (
                <ScrollView
                  className="max-h-40 mt-2 flex-grow-0 flex-shrink"
                  contentContainerStyle={{
                    flexGrow: 0,
                    paddingBottom: media.length === 1 ? 0 : 8,
                  }}
                  showsVerticalScrollIndicator={false}
                  keyboardShouldPersistTaps="handled"
                  nestedScrollEnabled={true}
                >
                  {media.map((post, index) => (
                    <Pressable
                      key={`media-${index}-${post.uri}`}
                      onPress={() => handleMediaPreview(post)}
                      className="flex-row gap-3 w-full mb-2 p-3 bg-gray-50 rounded-lg"
                      hitSlop={{ top: 4, bottom: 4, left: 4, right: 4 }}
                    >
                      <Image
                        source={{ uri: post.uri }}
                        style={{ width: 150, height: 120, borderRadius: 5 }}
                        resizeMode="cover"
                      />
                      <Text
                        className="leading-5 text-sm text-labelBlack flex-1"
                        numberOfLines={4}
                        ellipsizeMode="tail"
                      >
                        {post.caption}
                      </Text>
                    </Pressable>
                  ))}
                </ScrollView>
              )}
              {(isGenerating || isStreaming) && aiMode === 'global' && (
                <View className="pt-1">
                  <Text className="text-xs text-gray-500">
                    {isGenerating
                      ? 'AI is thinking about maritime content...'
                      : 'AI is writing your post...'}
                  </Text>
                </View>
              )}
            </View>
          )}

          {isCaptioning && (
            <ScrollView
              ref={scrollViewRef}
              className="flex-1"
              contentContainerStyle={{ flexGrow: 1, paddingBottom: 8 }}
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}
              bounces={true}
            >
              <View className="flex-1 px-4 pt-2">
                <View className="items-center justify-center mb-3">
                  <Image
                    source={{ uri: selectedImage! }}
                    style={{ width: 280, height: 180 }}
                    resizeMode="contain"
                    className="rounded-md"
                  />
                  <View className="flex-row flex-wrap justify-center gap-2 mt-2">
                    {media.map((post, index) => (
                      <PostThumbnail
                        key={index}
                        uri={post.uri}
                        isSelected={post.uri === selectedImage}
                        onPress={handleThumbnailPress}
                        handleTrash={handleDeletePost}
                      />
                    ))}
                  </View>
                </View>

                <View className="relative flex-1">
                  <TextInput
                    ref={textInputRef}
                    value={
                      (isGenerating || isStreaming) && aiMode === 'image' ? streamedText : postText
                    }
                    onChangeText={handleImageCaptionChange}
                    placeholder={
                      isGenerating
                        ? 'AI is thinking...'
                        : isStreaming
                          ? 'AI is writing...'
                          : 'Describe this image'
                    }
                    placeholderTextColor="#9ca3af"
                    multiline={true}
                    textAlignVertical="top"
                    autoFocus
                    maxLength={255}
                    editable={!isGenerating && !isStreaming}
                    blurOnSubmit={false}
                    returnKeyType={Platform.OS === 'ios' ? 'default' : 'none'}
                    scrollEnabled={true}
                    className="flex-1 text-base text-black leading-[22px] p-3 pb-9 text-top min-h-[200px] border border-gray-200 rounded-lg"
                  />

                  {(isGenerating || isStreaming) && aiMode === 'image' && (
                    <View className="absolute top-2 right-2">
                      <Pressable
                        onPress={stopGeneration}
                        className="bg-red-500 px-2 py-1 rounded-full"
                        hitSlop={{ top: 4, bottom: 4, left: 4, right: 4 }}
                      >
                        <Text className="text-white text-xs font-medium">Stop</Text>
                      </Pressable>
                    </View>
                  )}

                  <View className="absolute bottom-2 right-3">
                    <View className="bg-gray-100 px-2 py-1 rounded-full">
                      <Text
                        className={`text-xs ${postText.length > 255 * 0.9 ? 'text-orange-500' : 'text-gray-500'}`}
                      >
                        {postText.length}/255
                      </Text>
                    </View>
                  </View>
                </View>

                {(isGenerating || isStreaming) && aiMode === 'image' && (
                  <View className="pt-1">
                    <Text className="text-xs text-gray-500">
                      {isGenerating ? 'AI is analyzing...' : 'AI is writing your caption...'}
                    </Text>
                  </View>
                )}
              </View>
            </ScrollView>
          )}
        </View>

        <View
          className="absolute left-0 right-0 bg-white border-t border-gray-100 py-1.5 px-4"
          style={{ bottom: adjustedKeyboardHeight }}
        >
          <View className="flex-row gap-2">
            <Pressable
              onPress={() => handleShowAIOptions(isCaptioning ? 'image' : 'global')}
              className="flex-1 flex-row items-center justify-center py-2.5 px-3 bg-violet-50 rounded-lg border border-violet-200"
              hitSlop={{ top: 4, bottom: 4, left: 4, right: 4 }}
            >
              <Text className="text-violet-600 font-medium text-sm">⚓ Generate AI</Text>
            </Pressable>

            {((isCaptioning && hasImageText) || (!isCaptioning && hasGlobalText)) && (
              <Pressable
                onPress={handleShowEnhanceModal}
                className="flex-1 flex-row items-center justify-center py-2.5 px-3 bg-blue-50 rounded-lg border border-blue-200"
                hitSlop={{ top: 4, bottom: 4, left: 4, right: 4 }}
              >
                <Text className="text-blue-600 font-medium text-sm">✨ Enhance Text</Text>
              </Pressable>
            )}
          </View>
        </View>

        <BottomSheet
          visible={showAIBottomSheet}
          onClose={() => setShowAIBottomSheet(false)}
          onModalHide={() => {}}
          height={480}
        >
          {showAIBottomSheet && (
            <View className="flex-1 px-4 pt-2">
              <View className="flex-row justify-between items-center mb-3">
                <Text className="text-lg font-semibold text-gray-900">Generate with AI</Text>
                <Pressable
                  onPress={() => setShowAIBottomSheet(false)}
                  className="p-1 rounded-full active:bg-gray-100"
                  hitSlop={6}
                >
                  <Text className="text-gray-500 text-lg">✕</Text>
                </Pressable>
              </View>

              <View className="flex-row justify-between items-center mb-2">
                <Text className="text-gray-600 text-sm font-medium">Choose a maritime prompt:</Text>
                <Pressable
                  onPress={handleRefreshPrompts}
                  className="p-1 rounded-full active:bg-gray-100"
                  hitSlop={6}
                >
                  <Text className="text-violet-600 text-sm">🔄 Refresh</Text>
                </Pressable>
              </View>

              <ScrollView
                className="flex-1"
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ paddingBottom: 4 }}
                bounces={true}
              >
                {randomPrompts.map((prompt, index) => (
                  <Pressable
                    key={index}
                    onPress={() => handlePromptSelect(prompt)}
                    className="p-3 bg-gray-50 rounded-lg mb-2 border border-gray-200 active:bg-gray-100"
                  >
                    <Text className="text-gray-700 text-sm">{prompt}</Text>
                  </Pressable>
                ))}
              </ScrollView>

              <View className="flex-row gap-2 pb-1 pt-1">
                <Pressable
                  onPress={() => {
                    setShowAIBottomSheet(false);
                    setTimeout(() => setShowCustomPrompt(true), 500);
                  }}
                  className="flex-1 p-2.5 bg-blue-50 rounded-lg border border-blue-200 active:bg-blue-100"
                >
                  <Text className="text-blue-600 text-sm font-medium text-center">
                    ✏️ Custom Prompt
                  </Text>
                </Pressable>

                <Pressable
                  onPress={() => {
                    const surprisePrompt =
                      'Write a creative and engaging marine engineering, marine law etc related social media post';
                    handlePromptSelect(surprisePrompt);
                  }}
                  className="flex-1 p-2.5 bg-violet-50 rounded-lg border border-violet-200 active:bg-violet-100"
                >
                  <Text className="text-violet-600 text-sm font-medium text-center">
                    🎲 Surprise me!
                  </Text>
                </Pressable>
              </View>
            </View>
          )}
        </BottomSheet>

        <CustomPromptModal
          visible={showCustomPrompt}
          onClose={() => setShowCustomPrompt(false)}
          onSubmit={handleCustomPrompt}
        />

        <EnhanceTextModal
          visible={showEnhanceModal}
          onClose={() => setShowEnhanceModal(false)}
          onSubmit={handleEnhanceText}
          currentText={currentText}
        />

        <RateLimitModal visible={showRateLimitModal} onClose={() => setShowRateLimitModal(false)} />
      </View>
    </KeyboardAvoidingView>
  );
};

export default CreatePostForm;
