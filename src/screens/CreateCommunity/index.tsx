import SafeArea from '@/src/components/SafeArea'
import { KeyboardAvoidingView, Platform, View } from 'react-native'
import { ScrollView } from 'react-native-gesture-handler'
import CreateCommunityHeader from './components/CreateCommunityHeader'
import CreateCommunityForm from './components/CreateCommunityForm'
import { useNavigation } from '@react-navigation/native'
import { BottomTabNavigationI } from '@/src/navigation/types'

const CreateCommunityScreen = () => {
    const navigation = useNavigation<BottomTabNavigationI>()
    const handleNext = () => {
        navigation.navigate("Home")
    }
    return (
        <SafeArea>
            <KeyboardAvoidingView
                {...(Platform.OS === 'ios' ? { behavior: 'padding' } : {})}
                style={{ flex: 1 }}
            >
                <ScrollView contentContainerStyle={{ flexGrow: 1 }} keyboardShouldPersistTaps="always">
                    <View className='flex-1 px-5'>
                        <CreateCommunityHeader currentPage={1} onNext={() => handleNext()} />
                        <CreateCommunityForm />
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        </SafeArea>
    )
}

export default CreateCommunityScreen